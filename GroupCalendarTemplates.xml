<UI xmlns="http://www.blizzard.com/wow/ui/"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.blizzard.com/wow/ui/FrameXML/UI.xsd">
	
	<Frame name="CalendarDropDownTemplate" inherits="UIDropDownMenuTemplate" hidden="false" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentTitle" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="14" y="13"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="CalendarInputFrameTemplate" hidden="false" virtual="true">
		<Scripts>
			<OnShow>
				Calendar_InputFrameSizeChanged(this);
			</OnShow>
			<OnSizeChanged>
				Calendar_InputFrameSizeChanged(this);
			</OnSizeChanged>
		</Scripts>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentTopLeft" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="0" y="4"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0" right="0.046875" top="0" bottom="0.1875"/>
				</Texture>
				<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentTopLeft" relativePoint="BOTTOM"/>
					</Anchors>
					<TexCoords left="0" right="0.046875" top="0.1875" bottom="0.4375"/>
				</Texture>
				<Texture name="$parentBottomLeft" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentLeft" relativePoint="BOTTOM"/>
					</Anchors>
					<TexCoords left="0" right="0.046875" top="0.4375" bottom="0.625"/>
				</Texture>
				<Texture name="$parentTop" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentTopLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.046875" right="0.953125" top="0" bottom="0.1875"/>
				</Texture>
				<Texture name="$parentCenter" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.046875" right="0.953125" top="0.1875" bottom="0.4375"/>
				</Texture>
				<Texture name="$parentBottom" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentBottomLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.046875" right="0.953125" top="0.4375" bottom="0.625"/>
				</Texture>
				<Texture name="$parentTopRight" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTop" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0.953125" right="1.0" top="0" bottom="0.1875"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.953125" right="1.0" top="0.1875" bottom="0.4375"/>
				</Texture>
				<Texture name="$parentBottomRight" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.953125" right="1.0" top="0.4375" bottom="0.625"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="CalendarScrollbarTrenchTemplate" virtual="true">
		<Size>
			<AbsDimension x="27" y="261"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<Texture name="$parentTop" file="Interface\Addons\GroupCalendar\Textures\CalendarScrollbarTrench">
					<Size>
						<AbsDimension x="27" y="26"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.84375" top="0" bottom="0.1015625"/>
				</Texture>
				<Texture name="$parentMiddle" file="Interface\Addons\GroupCalendar\Textures\CalendarScrollbarTrench">
					<Size>
						<AbsDimension x="27" y="0"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTop" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.84375" top="0.1015625" bottom="0.90234375"/>
				</Texture>
				<Texture name="$parentBottom" file="Interface\Addons\GroupCalendar\Textures\CalendarScrollbarTrench">
					<Size>
						<AbsDimension x="27" y="25"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0" right="0.84375" top="0.90234375" bottom="1"/>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnSizeChanged>
				CalendarScrollbarTrench_SizeChanged(this);
			</OnSizeChanged>
		</Scripts>
	</Frame>

	<Button name="CalendarPlayerListItemTemplate" virtual="true">
		<Size>
			<AbsDimension x="115" y="15"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentText" text="playerName" inherits="GameFontHighlightSmallOutline">
					<Anchors>
						<Anchor point="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="5" y="3"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnClick>
				CalendarPlayerListItem_OnClick(arg1);
			</OnClick>
			<OnEnter>
				local	vTextItem = getglobal(this:GetName().."Text");
				
				vTextItem:SetTextColor(1.0, 1.0, 1.0);
			</OnEnter>
			<OnLeave>
				local	vTextItem = getglobal(this:GetName().."Text");
				
				vTextItem:SetTextColor(this.r, this.g, this.b);
			</OnLeave>
		</Scripts>
	</Button>
	
	<Frame name="CalendarPlayerListControl" hidden="false" virtual="true" inherits="CalendarInputFrameTemplate">
		<Size>
			<AbsDimension x="130" y="81"/>
		</Size>
		<Frames>
			<Frame name="$parentHighlightFrame" hidden="true">
				<Size>
					<AbsDimension x="110" y="15"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<Texture name="$parentTexture" file="Interface\QuestFrame\UI-QuestLogTitleHighlight" alphaMode="ADD"/>
					</Layer>
				</Layers>
			</Frame>
			<Button name="$parentItem0" inherits="CalendarPlayerListItemTemplate" id="0">
				<Anchors>
					<Anchor point="TOPLEFT"/>
				</Anchors>
			</Button>
			<Button name="$parentItem1" inherits="CalendarPlayerListItemTemplate" id="1">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem0" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
			<Button name="$parentItem2" inherits="CalendarPlayerListItemTemplate" id="2">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem1" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
			<Button name="$parentItem3" inherits="CalendarPlayerListItemTemplate" id="3">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem2" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
			<Button name="$parentItem4" inherits="CalendarPlayerListItemTemplate" id="4">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem3" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</Button>
			<ScrollFrame name="$parentScrollFrame" inherits="FauxScrollFrameTemplate">
				<Size>
					<AbsDimension x="105" y="75"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="0" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnVerticalScroll>
						gCalendarPlayerList_ActiveList = this:GetParent();
						
						FauxScrollFrame_OnVerticalScroll(
								gCalendarPlayerList_cItemHeight,
								CalendarPlayerList_ScrollUpdate);
					</OnVerticalScroll>
				</Scripts>
			</ScrollFrame>
		</Frames>
		<Scripts>
			<OnLoad>
				CalendarPlayerList_OnLoad();
			</OnLoad>
			<OnShow>
				CalendarPlayerList_OnShow();
			</OnShow>
		</Scripts>
	</Frame>
	
	<Button name="CalendarFrameTabTemplate" inherits="CharacterFrameTabButtonTemplate" virtual="true">
		<Scripts>
			<OnClick>
				PlaySound("igMainMenuOpen");
				PanelTemplates_Tab_OnClick(GroupCalendarFrame);
				GroupCalendar_ShowPanel(GroupCalendarFrame.selectedTab);
			</OnClick>
		</Scripts>
	</Button>
	
	<Button name="CalendarSmallButtonTemplate" virtual="true">
		<NormalText name="$parentText" inherits="GameFontNormalSmall"/>
		<DisabledText inherits="GameFontDisableSmall"/>
		<HighlightText inherits="GameFontHighlightSmall"/>
		<NormalTexture inherits="UIPanelButtonUpTexture"/>
		<PushedTexture inherits="UIPanelButtonDownTexture"/>
		<DisabledTexture inherits="UIPanelButtonDisabledTexture"/>
		<HighlightTexture inherits="UIPanelButtonHighlightTexture"/>
	</Button>
	
	<EditBox name="GroupCalendarInputBoxTemplate" inherits="InputBoxTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				CalendarInputBox_OnLoad(0);
			</OnLoad>
			<OnTabPressed>
				CalendarInputBox_TabPressed();
			</OnTabPressed>
		</Scripts>
	</EditBox>
	
	<EditBox name="GroupCalendarInputBoxTemplate2" inherits="InputBoxTemplate" virtual="true">
		<Scripts>
			<OnLoad>
				CalendarInputBox_OnLoad(1);
			</OnLoad>
			<OnTabPressed>
				CalendarInputBox_TabPressed();
			</OnTabPressed>
		</Scripts>
	</EditBox>
	
	<Button name="CalendarAttendanceMenuTemplate" virtual="true">
		<Size>
			<AbsDimension x="20" y="20"/>
		</Size>
		<Scripts>
			<OnClick>
				ToggleDropDownMenu(nil, nil, this);
				PlaySound("igMainMenuOptionCheckBoxOn");
			</OnClick>
			<OnHide>
				CloseDropDownMenus();
			</OnHide>
			<OnLoad>
				CalendarAttendanceDropDown_OnLoad();
				this.ChangedValueFunc = CalendarEventEditor_AttendanceMenuItemSelected;
			</OnLoad>
			<OnShow>
				CalendarAttendanceDropDown_OnLoad();
			</OnShow>
		</Scripts>
		<NormalTexture name="$parentNormalTexture" file="Interface\ChatFrame\UI-ChatIcon-ScrollDown-Up">
			<Size>
				<AbsDimension x="20" y="20"/>
			</Size>
			<Anchors>
				<Anchor point="RIGHT"/>
			</Anchors>
		</NormalTexture>
		<PushedTexture name="$parentPushedTexture" file="Interface\ChatFrame\UI-ChatIcon-ScrollDown-Down">
			<Size>
				<AbsDimension x="20" y="20"/>
			</Size>
			<Anchors>
				<Anchor point="RIGHT"/>
			</Anchors>
		</PushedTexture>
		<DisabledTexture name="$parentDisabledTexture" file="Interface\ChatFrame\UI-ChatIcon-ScrollDown-Disabled">
			<Size>
				<AbsDimension x="20" y="20"/>
			</Size>
			<Anchors>
				<Anchor point="RIGHT"/>
			</Anchors>
		</DisabledTexture>
		<HighlightTexture name="$parentHighlightTexture" file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD">
			<Size>
				<AbsDimension x="20" y="20"/>
			</Size>
			<Anchors>
				<Anchor point="RIGHT"/>
			</Anchors>
		</HighlightTexture>
	</Button>
	
	<CheckButton name="CalendarAttendanceItemTemplate" hidden="true" virtual="true">
		<Size>
			<AbsDimension x="218" y="16"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentCategory" inherits="GameFontNormal" justifyH="LEFT" justifyV="BOTTOM">
					<Size>
						<AbsDimension x="190" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="20" y="3"/>
							</Offset>
						</Anchor>
					</Anchors>
					<Color r="1" g="1" b="1"/>
				</FontString>
				<FontString name="$parentName" inherits="GameFontNormalSmall" justifyH="LEFT" justifyV="BOTTOM">
					<Size>
						<AbsDimension x="128" y="16"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="20" y="5"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString name="$parentStatus" inherits="GameFontNormalSmall" justifyH="RIGHT" justifyV="BOTTOM">
					<Size>
						<AbsDimension x="112"  y="16"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT" relativeTo="$parentName" relativePoint="BOTTOMRIGHT">
							<Offset>
								<AbsDimension x="2" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentAction" inherits="CalendarSmallButtonTemplate" text="GroupCalendar_cClear">
				<Size>
					<AbsDimension x="70" y="16"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentCategory" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="3" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound("igMainMenuOptionCheckBoxOn");
						if this.ActionFunc then
							this.ActionFunc(this.ActionParam);
						end
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentMenu" inherits="CalendarAttendanceMenuTemplate" hidden="true">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentStatus" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="2" y="-1"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
		</Frames>
		<NormalTexture file="Interface\Buttons\UI-MinusButton-Up">
			<Size>
				<AbsDimension x="16" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</NormalTexture>
		<PushedTexture file="Interface\Buttons\UI-MinusButton-Down">
			<Size>
				<AbsDimension x="16" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</PushedTexture>
		<CheckedTexture file="Interface\Buttons\UI-CheckBox-Check">
			<Size>
				<AbsDimension x="16" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</CheckedTexture>
		<DisabledCheckedTexture file="Interface\Buttons\UI-CheckBox-Check-Disabled">
			<Size>
				<AbsDimension x="16" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</DisabledCheckedTexture>
		<HighlightTexture name="$parentHighlight" file="Interface\Buttons\UI-PlusButton-Hilight" alphaMode="ADD">
			<Size>
				<AbsDimension x="16" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</HighlightTexture>
		<Scripts>
			<OnClick>
				CalendarAttendanceList_OnClick(this, arg1);
			</OnClick>
			<OnEnter>
				CalendarAttendanceList_OnEnter();
			</OnEnter>
			<OnLeave>
				CalendarAttendanceList_OnLeave();
			</OnLeave>
		</Scripts>
	</CheckButton>
	
	<Button name="CalendarExpandAllButton" virtual="true">
		<Size>
			<AbsDimension x="16" y="16"/>
		</Size>
		<NormalTexture file="Interface\Buttons\UI-MinusButton-UP">
			<Size>
				<AbsDimension x="16" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</NormalTexture>
		<HighlightTexture name="$parentHighlight" file="Interface\Buttons\UI-PlusButton-Hilight" alphaMode="ADD">
			<Size>
				<AbsDimension x="16" y="16"/>
			</Size>
			<Anchors>
				<Anchor point="LEFT">
					<Offset>
						<AbsDimension x="3" y="0"/>
					</Offset>
				</Anchor>
			</Anchors>
		</HighlightTexture>
	</Button>
	
	<Frame name="CalendarAttendanceListTemplate" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentTotal" text="" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="25" y="11"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		
		<Frames>
			<Button name="$parentExpandAllButton" hidden="false" inherits="CalendarExpandAllButton">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="6" y="-42"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						CalendarAttendanceList_ToggleExpandAll(this:GetParent());
					</OnClick>
				</Scripts>
			</Button>
			
			<Button name="$parentExpandAll" inherits="TabButtonTemplate" text="ALL" id="1">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="25" y="-28"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						PanelTemplates_SelectTab(this);
						PanelTemplates_TabResize(0);
						getglobal(this:GetName().."HighlightTexture"):SetWidth(this:GetTextWidth() + 31);
					</OnLoad>
					<OnClick>
						CalendarAttendanceList_ShowPanel(this:GetParent(), "MainView");
					</OnClick>
				</Scripts>
			</Button>
			
			<Button name="$parentGroupTab" inherits="TabButtonTemplate" text="CalendarEventEditor_cGroupTabTitle" id="2">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentExpandAll" relativePoint="TOPRIGHT"/>
				</Anchors>
				<Scripts>
					<OnLoad>
						PanelTemplates_DeselectTab(this);
						PanelTemplates_TabResize(0);
						getglobal(this:GetName().."HighlightTexture"):SetWidth(this:GetTextWidth() + 31);
					</OnLoad>
					<OnClick>
						CalendarAttendanceList_ShowPanel(this:GetParent(), "GroupView");
					</OnClick>
				</Scripts>
			</Button>
			
			<Frame name="$parentViewMenu" inherits="UIDropDownMenuTemplate" hidden="false">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="170" y="-28"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						GroupCalendarViewMenu_OnLoad();
						this.ChangedValueFunc = CalendarAttendanceList_ListViewItemSelected;
						this.AttendanceList = this:GetParent();
					</OnLoad>
					<OnShow>
						GroupCalendarViewMenu_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
			
			<Frame name="$parentMainView" setAllPoints="true" hidden="true">
				<Frames>
					<Button name="$parentAddButton" inherits="UIPanelButtonTemplate" text="GroupCalendar_cAddPlayerTitle">
						<Size>
							<AbsDimension x="90" y="21"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMRIGHT">
								<Offset>
									<AbsDimension x="-115" y="6"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick>
								PlaySound("igMainMenuOptionCheckBoxOn");
								CalendarAddPlayer_Open();
							</OnClick>
						</Scripts>
					</Button>
					<Frame name="$parentTotals">
						<Size>
							<AbsDimension x="315" y="54"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="0" y="26"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture setAllPoints="true" file="Interface\Addons\GroupCalendar\Textures\AttendanceSummaryBackground">
									<TexCoords left="0" right="1" top="0" bottom="0.84375"/>
								</Texture>
							</Layer>
							<Layer level="OVERLAY">
								<FontString name="$parentDruidsLabel" text="GroupCalendar_cDruidsLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativePoint="BOTTOMLEFT">
											<Offset>
												<AbsDimension x="75" y="47"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentDruids" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentDruidsLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentHuntersLabel" text="GroupCalendar_cHuntersLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentDruidsLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="100" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentHunters" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentHuntersLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentMagesLabel" text="GroupCalendar_cMagesLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentHuntersLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="100" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentMages" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentMagesLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentPaladinsLabel" hidden="true" text="GroupCalendar_cPaladinsLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentDruidsLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="-15"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentPaladins" hidden="true" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentPaladinsLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentShamansLabel" hidden="true" text="GroupCalendar_cShamansLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentDruidsLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="-15"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentShamans" hidden="true" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentShamansLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentPriestsLabel" text="GroupCalendar_cPriestsLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentHuntersLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="-15"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentPriests" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentPriestsLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentRoguesLabel" text="GroupCalendar_cRoguesLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentMagesLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="-15"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentRogues" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentRoguesLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentWarlocksLabel" text="GroupCalendar_cWarlocksLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentPriestsLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="-15"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentWarlocks" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentWarlocksLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentWarriorsLabel" text="GroupCalendar_cWarriorsLabel" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="TOPRIGHT" relativeTo="$parentRoguesLabel" relativePoint="TOPRIGHT">
											<Offset>
												<AbsDimension x="0" y="-15"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
								<FontString name="$parentWarriors" text="0" inherits="GameFontNormalSmall">
									<Anchors>
										<Anchor point="LEFT" relativeTo="$parentWarriorsLabel" relativePoint="RIGHT">
											<Offset>
												<AbsDimension x="2" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnShow>
								CalendarAttendanceTotals_OnShow(this);
							</OnShow>
						</Scripts>
					</Frame>
					
					<Frame name="$parentAutoConfirm" hidden="false">
						<Size>
							<AbsDimension x="315" y="23"/>
						</Size>
						<Anchors>
							<Anchor point="BOTTOMLEFT" relativeTo="$parentTotals" relativePoint="TOPLEFT"/>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture setAllPoints="true" file="Interface\Addons\GroupCalendar\Textures\AutoConfirmEnableBackground">
									<TexCoords left="0" right="1" top="0" bottom="0.71875"/>
								</Texture>
							</Layer>
						</Layers>
						<Frames>
							<CheckButton name="$parentEnable" inherits="OptionsCheckButtonTemplate">
								<Size>
									<AbsDimension x="26" y="26"/>
								</Size>
								<Anchors>
									<Anchor point="BOTTOMLEFT">
										<Offset>
											<AbsDimension x="12" y="-3"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnLoad>
										getglobal(this:GetName().."Text"):SetText(GroupCalendar_cEnableAutoConfirm);
									</OnLoad>
									<OnClick>
										local	vOptionsButton = getglobal(this:GetParent():GetName().."Options");
										
										Calendar_SetButtonEnable(vOptionsButton, this:GetChecked());
									</OnClick>
								</Scripts>
							</CheckButton>
							<Button name="$parentOptions" inherits="CalendarSmallButtonTemplate" text="GroupCalendar_cAutoConfirmButtonTitle">
								<Size>
									<AbsDimension x="90" y="18"/>
								</Size>
								<Anchors>
									<Anchor point="BOTTOMRIGHT">
										<Offset>
											<AbsDimension x="-15" y="2"/>
										</Offset>
									</Anchor>
								</Anchors>
								<Scripts>
									<OnClick>
										PlaySound("igMainMenuOptionCheckBoxOn");
										CalendarClassLimits_Open(gCalendarEventEditor_Event.mLimits, GroupCalendar_cAutoConfirmationTitle, false, CalendarEventEditor_SaveClassLimits);
									</OnClick>
								</Scripts>
							</Button>
						</Frames>
					</Frame>
				</Frames>
			</Frame>
			
			<Frame name="$parentGroupView" hidden="true">
				<Size>
					<AbsDimension x="315" y="54"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="0" y="26"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture setAllPoints="true" file="Interface\Addons\GroupCalendar\Textures\AttendanceSummaryBackground">
							<TexCoords left="0" right="1" top="0" bottom="0.84375"/>
						</Texture>
					</Layer>
					<Layer level="ARTWORK">
						<FontString name="$parentSelectionInfo" inherits="GameFontNormalSmall" justifyH="LEFT" justifyV="MIDDLE">
							<Size>
								<AbsDimension x="150" y="21"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="160" y="-7"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
						<FontString name="$parentStatus" inherits="GameFontNormalSmall" justifyH="LEFT" justifyV="MIDDLE">
							<Size>
								<AbsDimension x="150" y="21"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="160" y="-29"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentAutoSelect" inherits="CalendarSmallButtonTemplate" text="GroupCalendar_cAutoSelectButtonTitle">
						<Size>
							<AbsDimension x="140" y="21"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT">
								<Offset>
									<AbsDimension x="10" y="-7"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick>
								CalendarGroupInvites_AutoSelectPlayers();
							</OnClick>
						</Scripts>
					</Button>
					<Button name="$parentInvite" inherits="CalendarSmallButtonTemplate" text="GroupCalendar_cInviteButtonTitle">
						<Size>
							<AbsDimension x="140" y="21"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentAutoSelect" relativePoint="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="0" y="-1"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnClick>
								CalendarGroupInvites_InviteSelectedPlayers();
							</OnClick>
						</Scripts>
					</Button>
				</Frames>
			</Frame>
			
			<Frame name="$parentScrollbarTrench" inherits="CalendarScrollbarTrenchTemplate">
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset>
							<AbsDimension x="-3" y="-58"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			
			<CheckButton name="$parentItem0" inherits="CalendarAttendanceItemTemplate" id="0">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="5" y="-63"/>
						</Offset>
					</Anchor>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem1" inherits="CalendarAttendanceItemTemplate" id="1">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem0" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem2" inherits="CalendarAttendanceItemTemplate" id="2">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem1" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem3" inherits="CalendarAttendanceItemTemplate" id="3">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem2" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem4" inherits="CalendarAttendanceItemTemplate" id="4">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem3" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem5" inherits="CalendarAttendanceItemTemplate" id="5">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem4" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem6" inherits="CalendarAttendanceItemTemplate" id="6">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem5" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem7" inherits="CalendarAttendanceItemTemplate" id="7">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem6" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem8" inherits="CalendarAttendanceItemTemplate" id="8">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem7" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem9" inherits="CalendarAttendanceItemTemplate" id="9">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem8" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem10" inherits="CalendarAttendanceItemTemplate" id="10">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem9" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem11" inherits="CalendarAttendanceItemTemplate" id="11">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem10" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem12" inherits="CalendarAttendanceItemTemplate" id="12">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem11" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem13" inherits="CalendarAttendanceItemTemplate" id="13">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem12" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem14" inherits="CalendarAttendanceItemTemplate" id="14">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem13" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<CheckButton name="$parentItem15" inherits="CalendarAttendanceItemTemplate" id="15">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentItem14" relativePoint="BOTTOMLEFT"/>
				</Anchors>
			</CheckButton>
			<ScrollFrame name="$parentScrollFrame" inherits="FauxScrollFrameTemplate">
				<Size>
					<AbsDimension x="282" y="195"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="5" y="-63"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnVerticalScroll>
						gCalendarAttendanceList_VerticalScrollList = this:GetParent();
						FauxScrollFrame_OnVerticalScroll(gGroupCalendar_cAttendanceItemHeight, CalendarAttendanceList_OnVerticalScroll);
					</OnVerticalScroll>
				</Scripts>
			</ScrollFrame>
		</Frames>
		<Scripts>
			<OnLoad>
				CalendarAttendanceList_OnLoad();
			</OnLoad>
			<OnShow>
				CalendarAttendanceList_OnShow();
			</OnShow>
			<OnHide>
				CalendarAttendanceList_OnHide();
			</OnHide>
		</Scripts>
	</Frame>

	<CheckButton name="CalendarSideListItemTemplate" hidden="false" virtual="true">
		<Size>
			<AbsDimension x="280" y="16"/>
		</Size>
	</CheckButton>
</UI>
