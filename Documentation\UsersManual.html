<html>
	<head>
		<title>GroupCalendar User's Manual</title>
		<style type="text/css"> a:link { color: #ffe040; }
	a:visited { color: #ffe040; }
	a:hover { color: white; }
	a:active { color: purple; }
	body { background-image: url('Images/background.jpg'); background-repeat: repeat-xy; color: #ffe040; }
	h2 { color: white; }
	p { font-size: medium; font-weight: bold; margin-left: 3em; }
	div { font-size: medium; font-weight: bold; padding-left: 3em; }
	ul { margin-left: 5em; }
	li { font-size: medium; font-weight: bold; }
	.title { font-size: xx-large; font-weight: bold; color: #ffffff; }
	.note { color: #ffffff; margin-left: 2em; }
	.GameFontNormal { font-size: medium; font-weight: bold; color: #ffe040; }
	.GameFontHighlight { font-size: medium; font-weight: bold; color: #ffffff; }
	.Cmd { font-family: courier; font-size: medium; font-weight: normal; color: #ffffff; }
	</style>
	</head>
	<body>
		<span class="title"><img align="absMiddle" src="Images/CalendarIcon.jpg">GroupCalendar Instructions</span>
		<h2>Contents</h2>
		<ul>
			<li>
				<a href="#Install">Installing</a>
			<li>
				<a href="#Access">Accessing the calendar</a>
			<li>
				<a href="#Setup">Quick setup</a>
			<li>
				<a href="#Calendar">The calendar display</a>
			<li>
				<a href="#Attending">Viewing and attending an event</a>
			<li>
				<a href="#Add">Adding an event</a>
			<li>
				<a href="#Edit">Editing an event</a>
			<li>
				<a href="#Delete">Deleting an event</a>
			<li>
				<a href="#Attendance">Event attendance</a>
			<li>
				<a href="#AddPlayer">Adding players manually</a>
			<li>
				<a href="#Group">Party/Raid management</a>
			<li>
				<a href="#Ignore">Ignoring other player's events</a>
			<li>
				<a href="#Admin">Guild administrator setup</a>
			<li>
				<a href="#Manual">Manual setup</a></li>
		</ul>
		<h2><a name="Install">Installing</a></h2>
		<p>The GroupCalendar addon is installed just like any other addon. If World of 
			Warcraft is running you should exit from the game before installing. Extract 
			the contents of the GroupCalendar.zip file and then copy the resulting 
			GroupCalendar folder to your World of Warcraft's Addons folder located inside 
			the Interface folder.</p>
		<p><span class="GameFontHighlight">Windows users:</span>
			The Addons folder can normally be found at
			<span class="Cmd">C:\Program 
			Files\World of Warcraft\Interface\Addons</span></p>
		<p><span class="GameFontHighlight">MacOS users:</span>The Addons folder can 
			normally be found inside the Interface folder which is located inside the World 
			of Warcraft application folder</p>
		<p>IMPORTANT: Depending on your unzipping application, you may end up with the 
			addon folder inside another folder. Look inside the GroupCalendar folder and 
			verify that there's a file named GroupCalendar.toc. If you don't see this file 
			and see another folder instead, open that folder and look for the file. The 
			folder containing the .toc file is the one which should be copied to your World 
			of Warcraft addons folder.</p>
		<p>The addon is now installed and you can now launch World of Warcraft.</p>
		<h2><a name="Access">Accessing the calendar</a></h2>
		<p>There are two ways to open the calendar: either use the /calendar command or 
			click the calendar icon in the upper-right corner of your minimap. The calendar 
			icon replaces the sun and moon icons normally found on the minimap and also 
			replaces the time tooltip with a display of the current date and time, 
			including local time if it is different than the server time.</p>
		<h2><a name="Setup">Quick setup</a></h2>
		<p>If you are in a guild and a guild administrator has set up your guild for 
			automatic configuration then you will only need to make sure that you are set 
			to use automatic configuration. If you are not in a guild or your guild does 
			not use automatic configuration, skip to the section on Manual configuration to 
			set up the addon.</p>
		<p>To verify that the addon is set for automatic configuration, open the calendar 
			and click on the Channel tab at the bottom of the window.&nbsp; The checkbox 
			labeled "Automatic channel configuration" should be checked.&nbsp;</p>
		<div align="center"><img src="Images/ChannelSetup.jpg"></div>
		<p>If automatic configuration is working correctly, you will see a channel name and 
			a series of *****'s in the password field.&nbsp; At the bottom of the window 
			you should see the message "Data channel is connected."</p>
		<h2><a name="Calendar">The calendar display</a></h2>
		<p>The calendar will always open to the current month with an animated marquee 
			around the current date. To see the schedule for today or any other date simply 
			click on on that day in the calendar and that day's schedule will open 
			alongside the calendar.</p>
		<p>To choose a date in another month, use the arrow buttons located on either side 
			of the month name to switch to the previous or next month.</p>
		<P>To quickly return to the current date, use the downward pointing arrow in the 
			top right corner.</P>
		<P>GroupCalendar normally displays events using server date and time.&nbsp; If you 
			want to display and edit events using your local date and time, uncheck the Use 
			server dates and times checkbox.</P>
		<div align="center"><img src="Images/DaySummary.jpg"></div>
		<h2><a name="Attending">Viewing and attending an event</a></h2>
		<p>To view details for an event select the date and then select the event from the 
			list of events for that day.</p>
		<div align="center"><img src="Images/ViewEvent.jpg"></div>
		<p>From the event viewer you can sign yourself up for the event so that the event 
			organizer knows you want to attend.</p>
		<p>To sign yourself up choose which of your characters you want to attend on and 
			then check the Yes box.</p>
		<p>If you can't attend the event and want to let the organizer know, check the No 
			box.</p>
		<p>You can optionally add a comment to explain special circumstances for your 
			attendance. For example you may say that you're attending but that you'll be 
			late, or you may want to give a reason why you won't be attending.</p>
		<P>The Status at the bottom of the window shows what's happening with your 
			attendance request.&nbsp; Possible messages are:</P>
		<p>At the bottom of the event viewer is a status message. This will be one of 
			several message indicating the status of your attendance feedback:</p>
		<ul>
			<li>
				<span class="GameFontHighlight">No response sent</span>
			You haven't provided any response for this event
			<li>
				<span class="GameFontHighlight">Waiting for confirmation</span>
			You have sent a response but it hasn't been received and processed yet by the 
			event organizer
			<li>
				<span class="GameFontHighlight">Confirmed - Accepted</span>
			The event organizer has received your request and you have been added to the 
			event
			<li>
				<span class="GameFontHighlight">Confirmed - On Standby</span>
			The event organizer has received your request and has put you on the standby 
			list for the event
			<li>
				<span class="GameFontHighlight">Confirmed - Declined</span>
				The event organizer has received your request and has declined your request to 
				attend</li>
		</ul>
		<h2><a name="Add">Adding an event</a></h2>
		<p>To add an event click on the day that you want the event to take place, then 
			click the New Event button in the schedule window.</p>
		<div align="center"><img src="Images/AddEditEvent.jpg"></div>
		<p><b>Event</b> is used to set the icon for the event and will also be used as the 
			title if you don't provide a title of your own.</p>
		<p><b>Title</b> sets an optional title for the event</p>
		<p><b>Time</b> sets the start time for the event. Birthday events will not show a 
			Time field.</p>
		<p><b>Duration</b> sets how long the event is expected to last. Birthday events 
			will not show a Duration field.</p>
		<p><b>Levels</b> sets an optional level range for the event. Players who are not 
			within the specified level range will see the event as dimmed in their calendar 
			and will not be allowed to provide attendance feedback for the event. The level 
			range field is not display for Birthday or Meeting events.</p>
		<p><b>Description</b> sets an optional description for the event. This could be 
			used to clarify which part of a dungeon the event will cover or what a 
			meeting's agenda will be.</p>
		<P>To automatically sign yourself up for the event, check the "will attend" box at 
			the bottom of the window.</P>
		<p>Once you've set up your event, click the Done button to save it to the calendar. 
			If you change your mind and don't want the event, click the Delete button.</p>
		<h2><a name="Edit">Editing an event</a></h2>
		<p>To edit an event select the date in the calendar and then click the event in the 
			day schedule. You can then make any changes to the event and click the Done 
			button to save the changes to the calendar.</p>
		<h2><a name="Delete">Deleting an event</a></h2>
		<p>To delete an event select the date of the event in the calendar and then click 
			the event in the schedule window. In the event editor click the Delete button 
			and then confirm the deletion in the dialog which appears.</p>
		<h2><a name="Attendance">Event attendance</a></h2>
		<div align="center"><img src="Images/Attendance.jpg"></div>
		<p>GroupCalendar has the ability to track attendance responses to dungeon and 
			meeting events (there is not attendance feedback for birthday events). 
			Additionally, attendance requests can be processed automatically using 
			specified class limits or the requests can be manually processed.</p>
		<p>To use manual confirmations turn off Enable automatic confirmations. Users who 
			request to attend your event will always be placed into the standby list in 
			this mode. You can then use the menu next to their name to change their status 
			to Accepted or Declined if desired.</p>
		<p>To use automatic confirmations turn on Enable automatic confirmations. You can 
			then specify limits for your event using the Settings button. Those limits 
			instruct GroupCalendar how many people you want for your event as well as what 
			class composition you want.</p>
		<P>The Add button at the bottom of the attendance view can be used to manually add 
			players to the event.&nbsp; See the next section on manually adding players for 
			more information about this feature.</P>
		<div align="center"><img src="Images/ConfirmationLimits.jpg"></div>
		<p>When using limits, players will be confirmed for the event using these rules:</p>
		<p>If there's a minimum set for their class and that minimum hasn't been met, the 
			player is confirmed</p>
		<p>If there's a maximum set for their class and that maximum has already been 
			reached, the player is placed on standby</p>
		<p>If there's a maximum number of players set for the event and it hasn't been 
			reached then the player is confirmed</p>
		<P>Otherwise, the player is placed on the standby list</P>
		<p>This may seem confusing but hopefully you will find that players are accepted 
			for your group in the way you would expect. It's fairly easy to understand how 
			minimums work since that class will simply be filled in until the minimum is 
			reached. How spaces are given out between the minimum and maximum values may 
			seem a little confusing however. At the bottom of the limits window is the 
			total of all the minimums you've entered for all classes combined as well as a 
			setting for the maximum size for the event.&nbsp; The players between these 
			minimum and maximum values are considered "extras" and those extra slots will 
			be made available on a first-come-first-served basis to all players, as long as 
			the maximum for a player's class hasn't been reached. For example, if the total 
			of all the class minimums is 32 players and the maximum size of the raid 40 
			players, then there are 8 spaces available for extra players. These slots will 
			be given out to the first players who request them, unless the maximum for that 
			players class has already been reached.</p>
		<p>Using a minimum value for a class ensures that space will be reserved in your 
			event for that many players of the class.&nbsp; Leaving minimum blank is the 
			same as setting the minimum to zero.</p>
		<p>Using a maximum value for a class ensures that you won't fill all of your extra 
			spaces with only that class.&nbsp; Leaving maximum blank means that there is no 
			maximum for the class and that it's ok for all of the extra spaces to be filled 
			by that class.</p>
		<P>If you don't want any players at all of a certain class, set the maximum for 
			that class to zero.</P>
		<h2><a name="AddPlayer">Adding players manually</a></h2>
		<p>To add a player manually to an event first select the event and then go to the 
			Attendance tab.</p>
		<p>You can then add players in one of two ways. The basic way is to click the Add 
			button at the bottom of the attendance view and fill in the various fields of 
			the Add Player dialog. If the player is in your guild then you only need to 
			provide their name and the other fields will be filled in automatically.</p>
		<p>Alternatively you can add players via whispers. This is especially useful if 
			your guild customarily does signups for events by having player whisper for 
			invites at the start of the event rather than signing up in advance. At the top 
			of the attendance list for the event there will be a category named Recent 
			Whispers (this category won't be present if you aren't the creator of the event 
			or if nobody has whispered you since you logged on). Use the menu next to the 
			first whisper and choose Add Player to bring up the Add Player dialog.</p>
		<div align="center"><img src="Images/AddWhisper.jpg"></div>
		<p>Here you can see the last whisper from that player and decide what to do with 
			them. If you want to add them to your event, fill in any missing fields and 
			choose whether they should be set to Accepted or to Standby. Optionally, you 
			can provide a response whisper, usually a confirmation that they've been added. 
			This response whisper will be remembered and used for subsequent players as you 
			add them.&nbsp;Click the Save button to add the player to the event and that 
			whisper will be removed and the next whisper will automatically be displayed.</p>
		<p>If the whisper isn't related to the event you can click the Delete button and it 
			will be discarded and the next whisper will be displayed</p>
		<p>If you don't want to process any more whispers, click the Done button to save 
			and exit from the dialog or the Cancel button to exit without doing anything 
			with the current whisper.</p>
		<h2><a name="Group">Party/Raid management</a></h2>
		<p>GroupCalendar can assist you with putting your party or raid together for your 
			event. It can automatically choose players for your party, invite those players 
			to your group, and help fill extra spaces when people have to leave.</p>
		<p>To access the party/raid management features, open your event and go to the 
			Attendance tab. There you will see two tabs on the attendance list, All and 
			Group. Select the Group tab.</p>
		<div align="center"><img src="Images/Group.jpg"></div>
		<p>This view shows all of the attendees who are either accepted or on standby for 
			the event as well as any players currently grouped with you, including your own 
			character. All players will show a status in parenthesis next to their name. 
			Possible values are:
			<ul>
				<li>
					<span class="GameFontHighlight">Joined</span>
				The player is in your party or raid. Note that your own character will always 
				show this as its status
				<li>
					<span class="GameFontHighlight">Ready</span>
				You have accepted the player for this event but have not yet invited them to 
				your party or raid
				<li>
					<span class="GameFontHighlight">Standby</span>
				You have accepted the player as a standby for this event but have not yet 
				invited them to your party or raid
				<li>
					<span class="GameFontHighlight">Invited</span>
				You have invited the player to your party or raid but they have not yet joined 
				the group
				<li>
					<span class="GameFontHighlight">In another group</span>
				You have invited the player to your party or raid but they are already in 
				another group
				<li>
					<span class="GameFontHighlight">Declined invitation</span>
				You have invited the player to your party or raid but they declined the 
				invitation
				<li>
					<span class="GameFontHighlight">Offline</span>
				The player is offline
				<li>
					<span class="GameFontHighlight">Left group</span>
					The player had joined your party or raid, but has since left the group</li>
			</ul>
		<p>To start forming your group you must first select the players you want to have 
			join. You can manually select players by using the checkbox next to their name. 
			Note that if they're already in your party or raid there won't be a checkbox 
			and you can't select them.</p>
		<p>To automatically select players click the Select Players button. This will give 
			you a class limits dialog very similar to the one used for automatic 
			confirmations. Read the section above about how automatic confirmations work 
			for an explanation of how the limits in this dialog work as well.</p>
		<div align="center"><img src="Images/AutoSelect.jpg"></div>
		<p>The automatic selection dialog has one additional setting for Priority. Use this 
			to determine if players should selected based only on their signup date or if 
			top priority should be given to higher guild ranks with signup date used to 
			determine who is selected within each rank.&nbsp; Note that guild rank 
			priorities will not work properly if you have players who are not in the same 
			guild as your character.</p>
		<p>To invite the selected players click the Invite Selected button. This will 
			automatically whisper everyone who is selected telling them what they're being 
			invited to as well as sending them the actual group invite. If they are already 
			in a group and therefore can't be invited then they will receive a second 
			whisper telling them this. You will need to re-invite them once they inform you 
			that they've&nbsp;left their group.</p>
		<p>If your group is going to be a raid then only the first four invites will be 
			sent initially. Once any of these players has accepted then the party will 
			automatically be converted to a raid and the remaining invites will be sent.</p>
		<h2><a name="Ignore">Ignoring other player's events</a></h2>
		<p>If you don't want to see events posted by a particular player you can ignore 
			them by adding their name to the Exclude list in the Trust tab. To add them, 
			enter their name into the Player Name field and click the Exclude button.</p>
		<p>If you later change your mind you can remove them from the Exclude list by 
			selecting their name and clicking the Remove button.</p>
		<div align="center"><img src="Images/TrustSetup.jpg"></div>
		<h2><a name="Admin">Guild administrator setup</a></h2>
		<p>If you are an officer of your guild with the ability to set members public notes 
			then you can also act as an administrator for GroupCalendar for your guild. 
			This will allow you to specify the channel, channel password and trust settings 
			for members of your guild, making setup automatic for them. This will also 
			allow you to easily change the data channel name and password for the entire 
			guild at once rather than having to pass the new information on to each member 
			individually.</p>
		<p>On the Channel tab, set the mode to Manual Configuration and enter a channel 
			name and a password for that channel. This is the chat channel which will be 
			used to pass calendar event data between members of your guild.</p>
		<p>Turn on the Store Auto-config Data in Player Note checkbox and enter the name of 
			one of the members of your guild. It doesn't matter which member it is and 
			using an otherwise meaningless member such as a guild mule is a good idea. The 
			public note for that player will be changed to a configuration string which 
			GroupCalendar will automatically find and use to configure itself for your 
			members.</p>
		<div class="note">NOTE: When you need to change the data channel password you 
			should also change the name of the data channel. This is because their is no 
			real concept of channel ownership in World of Warcraft (ownership is 
			arbitrarily passed to someone else in the channel when the current owner logs 
			off). This makes it very difficult to change only the password, so whenever 
			changing the channel configuration always change both the name and password for 
			the channel.</div>
		<p>Finally, go to the Trust tab and set the trust group to Guild Members Only and 
			set the minimum rank at which people are allowed to post events to the 
			calendar.</p>
		<div class="note">NOTE: If you are in an alliance with one or more other guilds and 
			want to share calendars with them, you should set the trust group to Anyone Who 
			Has Access to the Data Channel. Their guild administrator should set their 
			configuration to match yours. Also, if you need to change the data channel then 
			you will need to notify their administrator so they can change it for their 
			guild as well.</div>
		<h2><a name="Manual">Manual setup</a></h2>
		<p>If you are not in a guild or are in a guild which doesn't use the automatic 
			setup feature then you will need to manually configure the channel and trust 
			settings.</p>
		<p>In the Channel tab set the calendar for Manual Channel Configuration and enter 
			the channel name and password for the chat channel you want to use to exchange 
			calendar data.</p>
		<p>In the Trust tab set the trust group to either Anyone or Only Those Listed Below 
			depending on your preference. If you set it to Only Those Listed Below then you 
			will need to manually add each person you want to share calendars with to the 
			Additional Players list. Simply enter their name in the Player Name field and 
			click the Trust button to add them.</p>
	</body>
</html>
