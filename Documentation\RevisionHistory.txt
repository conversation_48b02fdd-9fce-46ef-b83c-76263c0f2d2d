------------------------------------------------------------------------
Bug reports for 2.0 beta:
------------------------------------------------------------------------
Show attendance total as accepted+standby
Show class total as accepted+standby
------------------------------------------------------------------------
When I create a new event and enter a custom event name, even though I have my
'attend' checkbox off, G<PERSON> will sign me up as attending and I need to edit my
attendance. If I create a new event without entering a custom event name (ie
the addon automatically remmebers one), it will not 'attend' me if the checkbox
is off.
------------------------------------------------------------------------
If you select an event, then click on attendance, in the upper left hand corner
theres a + button, clicking on it makes it into a - button but does nothing
else. Clicking on it again after that works as expected opening/closing all header
sections
------------------------------------------------------------------------
When going to the about tab to find versions I was completely missing my wife
who is running 2.1b4 on her computer also. I clicked refresh nothing happened.
(Might have been during inital 5min window) Many hours later I went back and
it still didn't show her, clicking on refresh I was eventually able to see her
information. Shouldn't this be auto-populating? Hers also showed me as using
2.1b3 until she clicked refresh.
------------------------------------------------------------------------
When an events description is too long it runs under the dropdown for Character
Select and that portion is unreadable.
------------------------------------------------------------------------
Modify event forwarded so that untrusted guild members can still forward events for others
------------------------------------------------------------------------

European default time format
add short command for opening /cld

v2.2.1 Changes:

- Updated name of Ahn'Qiraj Temple reset event for patch 1.11
- Fixed bug which could cause the event creator's self-attendance to not show up on other people's calendars
- Moved description up slightly in the event viewer for better appearance

v2.2.1b2 Changes:

- Fixed escaping of special characters in attendance comments
- Fixed setting default time for new events to use previous time again
- Corrected display problem with AQ20 and AQ40 dog-ear icons

v2.2 Changes:

- Added Naxxramas, general PvP and Roleplaying event types
- Player kill command no longer allows a guild member to be specified
- Fixed instance reset names for Onyxia's Lair, Zul'Gurub and Ruins of Ahn'Qiraj (English clients)
- Fixed instance reset names for Onyxia's Lair (German clients)
- Fixed instance reset names for Onyxia's Lair and Molten Core (French clients)
- Updated Simplified Chinese translation

v2.2b3 Changes:

- Adjusted level range boxes for better fit
- Changed font setting for event creator name to use WoW constants

v2.2b2 Changes:

- Updated Simplified/Traditional Chinese translations
- Updated Onyxia's Lair string for German translation

v2.1.1 Changes:

- Fixed raid reset handling for patch 1.11
- Added ability for guild officers to purge a player's events from the network
- Now displays the next four resets for an instance

v2.1 Changes:

- Support for users logging in from multiple PCs
- Corrected detection of Onyxia's Lair for German clients
- Fixed class coloring for non-English clients
- Added UI to About tab for viewing Group Calendar versions being run by other players
- Disabled sending of player alts when replying to an event
- Players removed from an event will now display a "Banned from event" message so they
  know why their attendance requests aren't being handled any more
- Improved event and attendance request reliability

v2.1b9 Changes:

- Changed version polling to a one-time event to prevent channel spam
- Fixed bug causing event and attendance update problems when using guild rank-based trust settings

v2.1b8 Changes:

- Added version polling every 20 minutes
- Modified trust handling so that untrusted guild members are still allowed to forward events

v2.1b7 Changes:

- Fixed bug with processing attendance requests from the same player for different events

v2.1b6 Changes:

- Added check for nil pointer when receiving a confirmation settings update for an
  event which no longer exists
- Corrected French translation for Herbalism tradeskill

v2.1b5 Changes:

- Actually changed default trust (turned out I made the change to the wrong source code in 2.1b3, oops :)
- Fixed expand/collapse all button in attendance view

v2.1b4 Changes:

- Updated French translation
- Removed debug messages

v2.1b3 Changes:

- Changed default trust for unguilded players to trust anyone in channel.  This should make things simpler for first time users
- Added support for high priority synching of alts data
- Fixed bugs causing some player updates to be sent before five minute synch was completed
- Fixed display problem with player version list
- Added channel status message to indicate when synching is still occurring
- Fixed bug causing some event updates to fail to appear in the calendar after processing
- Added synchronization warning dialog if the user edits events during synch phase

v2.1b2 Changes:

- Changed RSVP handling so that new attendance requests with older dates aren't discarded
  but instead are added as if they are one second newer than the previous request
  
v2.0b17 Changes:

- Fixed bug causing case sensitivity in channel names
- Fixed bug causing update requests to be lost during the five minute timeout
  period after login
  
v2.0b16 Changes:

- Fixed another bug in processing request queues

v2.0b15 Changes:

- Updated TOC for patch 1.10
- Turned off debug code

v2.0b14 Changes:

- Fixed bug with processing request queues which could cause large backups
  of requests and sporadic freezes lasting from less than a second to 30
  seconds or more.  The frequency and severity of this problem is related
  to the number of users on the network and therefore primarily affected
  larger guilds.

v2.0b13 Changes:

- Fixed typo causing nil pointer error in EventDatabase.lua

v2.0b12 Changes

- Fixed bug causing event IDs to become strings which resulted in events not being
  located properly for attendance processing
- Fixed bug causing RSVPs to stay around after the event or database they're for
  are deleted

v2.0b11 Changes

- Fixed player attendance comment to unescape special characters
- Fixed minor race condition which could cause initialization to
  halt when data channel is already joined
- Fixed attendance request processing to correctly remove processed
  requests from the queue
- Fixed bug causing attendance requests to be lost when making a Yes/No
  response and then immediately changing to the opposite response
- Improved event/attendance processing performance when the calendar UI
  isn't open

v2.0b10 Changes

- Delayed initial event update requests until after the guild roster is received
  when trust setting is Guild Members.  This should prevent problems with unreliable
  event delivery when using that trust setting.

v2.0b9 Changes

- Modified event viewer to disable comment field with neither Yes nor No are checked
- Fixed recognition of tradeskill events and tradeskill window opening so that cooldowns
  are properly scheduled
- Added timeout to calendar updates to prevent a disconnected player from causing
  updates to becomed locked out
- Disabled Invite button when player isn't capable of inviting
- Modified event viewer so that comment-only attendance feedback isn't sent
- Modified roster updating do refresh every four minutes so that rank-based authentication
  is more reliable

v2.0b8 Changes

- Fixed Ahn'Qiraj Ruins reset event detection
- Fixed processing of RSVPs so that comment updates don't cause the player to lose
  their date/time priority or accepted/standby status
- Fixed invites so that they aren't limited by the event attendance settings but are
  limited by the number of players selected
- Added Salt Shaker and SnowMaster 9000 support for tradeskill cooldown events
- Removed filtering of whispers before an event is opened.  This was causing too much
  trouble with missing invite requests when the organizer hadn't opened the event yet.

v2.0b7 Changes

- Fixed bug which caused a players rank to fail to update in the group list after updating
  the rank in the attendance list
- Added View by Name to attendance list
- Fixed dungeon reset event scheduling for Ahn'Qiraj Temple and Ruins (unverified)

v2.0b6 Changes

- Fixed bug causing menus to change suddenly while choosing items
- Fixed bug which prevented changing a players rank in the attendance list under certain conditions
- Fixed display of long event titles in the event viewer
- Fixed bugs in automatic invites which would cause invites to stop being issued
- Offline guild and party members will have their names rendered in gray in the event Group list
- Fixed bug causing event Group list status to not update when raid members changed

v2.0b5 Changes

- Fixed sorting of events when events are on different days in server date/time but
  on the same date in local date/time
- Fixed Rebuild Database so that private events (tradeskill cooldowns and instance
  resets) aren't added to the public calendar
- Fixed bugs in filtering whispers from existing event attendees/party members
- Fixed bug which caused Enable Automatic Confirmations checkbox to revert to its
  original setting whenever attendance for an event was updated
- Prefixed automatic event invite whispers with [GroupCalendar]
- Modified group invites to do them at a slower rate so that Blizzard's spammer
  detection sentry's don't mistakenly kick the event organizer offline
- Fixed event viewer so that attendees don't send a request to delete themselves
  from an event when they're on the Standby list

v2.0b4 Changes

- Fixed event editing in local time zone for events where local date
  is different from server date
- Added whisper filtering to eliminate recording whispers from existing
  attendees and party members

v2.0b3 Changes

- Modified Recent Whispers to ignore whispers starting with [ < or {
- Added Clear button to Recent Whispers category
- Fixed attendance menu items so they work correctly without Outfitter installed

v2.0b2 Changes

- Updated user's manual for 2.0 features
- Fixed nil pointer bug when handling alts list in attendance responses
- Fixed bug causing request queue to be processed much more slowly than
  intended.  This was especially noticeable at startup because channel
  initialization was delayed for a very long time.
- Fixed bug causing attendance responses not to be requested at startup if
  trust is set to guild members only
- Modifed protocol to suppress sending updates for three minutes after login
- Added task queue for managing tasks which should run independently of
  channel traffic

v2.0b1 Changes

- 2.0b1 is the same as 2.0a6

v2.0a6 Changes

- Fixed automatic party selection so that the priority is actually based on
  the setting from the limits dialog and not on the view mode of the list

v2.0a5 Changes

- Fixed class/race dropdowns to be faction-specific
- Implemented Remove RSVP
- Added class limits defaults
- Added class limits and manual/auto confirm setting to saved templates
- Added default limits to auto select when event has none specified
- Changed recent whispers to only show up on events which are editable

v2.0a4 Changes

- Fixed item coloring of member of Unknown class
- Fixed group expand/collapse

v2.0a3 Changes

- Fixed group invite

v2.0a2 Changes

- Fixed nil pointer bug when attempting to open events

v2.0a1 Changes

- Decreased maximum event age to 30 days from 45 days
- Fixed time wrapping so that events which cross over midnight display the correct end time

v1.2.1 Changes

- Changed server time zone calculation to correct for players more than 12 hours off from the server

v1.2b4 Changes

- Added "circled" dates and times for events being attended
- Fixed bug causing attendance feedback to get reset sometimes while trying to change it
- Modified to hide the "use server time" checkbox when server and local time are the same

v1.2b3 Changes

- Today and Date buttons now toggle the summary window
- Hooked up guild rank caches to improve performance
- Modified database trust checking to be a scheduled event instead of
  immediate in order to improve performance
- Modified queues to be processed more steadily in an attempt to reduce
  unnecessary traffic and smooth out potential lag spikes

v1.2b2 Changes

- Modified database code to use an "LocalUsers" tag.  A character will not use
  a database until he sees it offered on his own network.  This should prevent
  unguilded toons from clearing the database cache as well as preventing toons
  in other guilds from spreading events between the guilds.
  
v1.2b1 Changes

- Added event cleanup so that events older than 45 days are automatically
  removed, except for birthdays which are rescheduled for the same time next year
- Added ability to display and edit events in local date and time instead
  of server date and time

v1.1 Changes

- Changed dungeon order to be by level instead of by English alphabetical
  order
- Added Simplified and Traditional Chinese translations
- Fixed bug with Trust tab names not working correctly for names that
  didn't start with a Roman letter
- Fixed crash when switching to windowed mode on Macintosh clients
- Fixed minor layout bugs

v1.1b23 Changes

- Fixed nil pointer bug when reconstructing databases from their
  change history

v1.1b22 Changes

- Fixed nil pointer problem whene executing attendance changelists
- Fixed bug which caused events which don't have a type to not be
  added correctly to other people's calendars
- Added icon for events which don't have an event type
- Changed calendar button to use the GameTimeFrame as a parent so that it hides
  or moves if a UI changes the time frame
- Fixed store auto config so that the config data isn't cleared accidentally
  if an officer uses manual config but doesn't select a config player

v1.1b21 Changes

- Added Battlegrounds events (I know, I shouldn't be adding features
  this late but this really belonged in 1.0 with the other dungeons
  and it was only omitted because of my light battlegrounds experience)
- Modified calendar icon rendering to avoid icons for events it doesn't
  understand.  This will allow new event types to be added in the future
  without causing the calendar to render ugly green squares.

v1.1b20 Changes

- Added support for key binding to show/hide the calendar.  This was a trivial
  change which should make life a little easier for people using heavily
  customized UIs.
- Updated BFD icon with new artwork
- Changed database ID to be a timestamp instead of a sequence number.  This fixes
  Rebuild Database so that you only have to hit it once to make it clear up the
  network and also fixes initialization so that throwing away your settings file
  will clear your old database from the network.
- Modified update handling so that changes are only made after the update is
  received.  This fixes problems with partial updates being sent out on the
  network while they were still in progress as well as fixing problems caused
  by updates being promised but never arriving.

v1.1b19 Changes

- Fixed bug causing guild trust level to be calculated incorrectly

v1.1b18 Changes

- Increased startup delay to 2 minutes but added code which shortens it
  to 10 seconds when the General channel is joined.  This should make
  startup quick for people who don't experience significant login lag
  but still be long enough to ensure that /1 doesn't go to the calendar
  channel if there is a lot of lag
- Fixed bug causing attendance requests to be ignored by the event owner
  under some circumstances
- Fixed excess traffic when requesting authoritative updates
- Fixed live updating of UI when new event data arrives
- Modified trust UI to hide rank dropdown when trust group isn't set to guild
- Modified trust UI to update when autoconfig data arrives
- Fixed change notification on deleted events

v1.1b17 Changes

- Modified attendance list to show queued attendance requests
  as "Pending approval"
- Changed minimap icon to be smaller, more discrete so that the sun and
  moon could shine through
- Changed calendar icon to be more in the spirit of the game's design
- Fixed bug in auto-config trust level.  This may be responsible for
  events not showing up or attendance requests not getting confirmed.
- Fixed local time calculation when server time is near midnight
- Updated Ruins of Ahn'Qiraj icon

v1.1b16 Changes

- Increased delay until channel initialization after login
- Added more status messages during initialization

v1.1b15 Changes

- Changed channel initialization to provide detailed error information when
  the password fails or the number of allowed channels is exceeded

v1.1b14 Changes

- Fixed local time calculation so it occurs on PLAYER_ENTERING_WORLD event
  instead of VARIABLES_LOADED.  The server time value doesn't appear to be
  reliable at VARIABLES_LOADED time.
- Restored the old Dire Maul event icon
- Lowered the saturation of BRD, MC and RFC icons for better appearance
- Modified RebuildDatabase to repair event IDs if there are any collisions
- Fixed bug causing changes to be lost during update event processing.  This
  was causing events to disappear right after they were created but could
  also cause other problems such as attendees disappearing or changes to
  events reverting to the original event values.
- Added support for separate update request for owner revision and shared revision

v1.1b13 Changes

- Fixed bug which caused drunken chat strings to be escaped in a different
  format than they were being un-escaped, causing damaged messages.  I 
  really shouldn't program at 2am, really.

v1.1b12 Changes

- Updated Onyxia icon with new artwork
- Added check for invalid packet on UPD (DB or RAT) message
- Added support for intoxicated players
- Changed protocol to GC2 since it isn't compatible with the drunk escaping

v1.1b11 Changes

- Fixed *another* bug causing nil pointer errors during certain updates

v1.1b10 Changes

- Fixed bug causing nil pointer error during certain updates
- Added attendance total to attendance panel

v1.1b9 Changes

- Modified event updates to store actual change log directly, then
  create event updates from that.  This should improve reliability
- Modified event updates to track owner updates and reconstruct the
  database if the owner provides data that differs from proxied updates
- Fixed Rebuild Database to correctly rebuild the attendance records

v1.1b8 Changes

- Fixed bug which caused channel to not be closed when quitting in an inn

v1.1b7 Changes

- Fixed database rebuild when receiving major database revision changes
- Modified attendance viewer to hide class totals on non-questing events
- Added newline to list of escaped characters to prevent random disconnects

v1.1b5 Changes

- Fixed an error when logging out while not connected to any channel
- Updated icons with new artwork form Palyr for BFD, BWL, DM, UBRS, ZG
  and Meetings
- Added background image to event viewer and editor

v1.1b4 Changes

- Fixed bugs which caused redundant network requests to be issued
- Fixed database rebuild so a revision isn't opened when there are
  no events or RSVPs
- Fixed bug which could cause an external database to become corrupted
  during updates
- Changed database format to 4 to force a rebuild and flush old
  databases to clear any corruption problems
- Fixed error regarding nil concatenation caused by database errors
- Changed initialization sequencing to avoid excessive loading of the
  guild roster
- Added guild roster cache to speed up trust lookup
- Fixed bug causing attendance items to show highlights where there was
  nothing to highlight
- Attendance list names are now concatenated with their level and class
  name to conserve space

v1.1b1 Changes

- Attendance feedback comments are now displayed as a tooltip in the
  Attendance panel
- Added a new Today button in the main calendar display to jump directly
  to the current date
- Special characters are now escaped in event titles and descriptions
  and feedback comments so they can be properly transmitted
- Added 5 and 6 hour choices to the Duration menu
- Attendance list for meetings is now sorted by is/isn't attending instead
  of by class
- Attendance list names are now sorted alphabetically
- Minimap date/time tooltip now also displays the day of the week
- Birthday events no longer show an attendance tab 

v1.0.2 Changes

- Added support for 24 hour times in the event editor
- Moved dungeon names into GroupCalendarStrings.lua
- Modified class and race name/id lookup to be friendlier to localizers
- Moved class names, race names, and class color indices to
  GroupCalendarStrings.lua for localization
- Added French and German translations

v1.0.1 Changes

- Modified trust tab to save auto-config data when changing settings

v1.0 Changes:

- Changed trust group settings to be part of the auto-configuration data

v1.0f4 Changes:

- Changed protocol ID to GC1.  This will prevent this version from communicating
  with older versions, please upgrade to this version to continue getting
  calendar event updates
- Added check for nil ID before bumping changelist revision
- Modified user authentication to fail gracefully and silently when the guild
  roster hasn't been loaded yet

v1.0f3 Changes:

- Modified the protocol slightly to allow for empty databases to be deleted
  and not transmitted over the data channel.  This should reduce both memory
  and data channel chatter by a fair amount

v1.0f2 Changes:

- Changed new alliance race from Pandarin to Draenaei
- Fixed minor bug causing empty databases to be announced to the channel
- Added work-around for bug in which the ClearFocus method of edit fields
  isn't always present

v1.0f1 Changes:

- Fixed bug which caused RSVP database to fail to update correctly after a
  database rebuild
- Modified rebuild database to compact the RSVP database as well

v1.0b13 Changes:

- Modified channel shutdown during logout to avoid a spurious error message
- Fixed bug with database IDs not updating properly
- Changed protocol to GC3
- Changed database to version 3 to force all external databases to be
flushed
- Modified channel suspension during logout to prevent channel number from
becoming invalid
- Fixed various uses of the guild roster to exit gracefully when the roster
hasn't been downloaded yet

v1.0b12 Changes:

- Modified channel status message to display in red/green/yellow based on the status
- Added animated highlight to today's date in the main calendar display
- Fixed a bug in the Disconnect button which caused it to reconnect after
  closing the window or changing tabs
- Modified request processing queue to suspend itself while incoming messages
  are being processed.  This should reduce redundant data being sent over the channel
- Modified event editor so it doesn't save events which don't have a type or title
- Added support for "quick" event changes to help reduce network traffic
- Fixed a bug which could cause unnecessary requests to be issued for the same update
- Modified channel password field when auto-config is enabled to show "******"
  to indicate when a channel password is being used
- Fixed bugs causing certain network messages to be sent even when the same message
  was already sent by another player

v1.0b11 Changes:

- Added ability to connect/disconnect from the data channel
- Added channel connection status
- Changed auto-configuration on guild roster change to a deferred event to avoid any
  performance issues related to frequent guild roster updates

v1.0b10 Changes:

- Added inbound message queue
- Suspended outbound message processing while inbound messages are being received
- Disabled update events when all queues are empty

v1.0b9 Changes:

- Added ability to rebuild the players database using a button on the
  about page

v1.0b8

- Added about screen
- Removed use of QuestLog UI template for show/hide all button
  
v1.0b7 Changes:

- Fixed bug causing player level to not be set correctly which caused
  events with level restrictions to be blocked

v1.0b6 Changes:

- Fixed bug causing update events to trigger an error dialog

v1.0b5 Changes

- Removed annoying message about auto-config data not found
- Players who do not meet the level requirements for an event will see
the event's icon as translucent in their calendar
- Players who do not meet the level requirements for an event will not be
able to provide an attendance response for an event
- Modified trust behavior so that fellow guild members can respond to and
view events even if they're not trusted to provide events
- Modified event viewer to display level range in red when player isn't
qualified
- Modified security to allow all guild members to request calendar 
information if the trust group includes guildies (only guild members of the 
specified rank or higher can contribute to your calendar however)

v1.0b4 Changes

- Fixed bug which prevented giving attendance response to events
- Fixed "store auto config in player" option to remove data from the old
player setting
- Fixed edit fields so they give up keyboard focus before being disabled
- Added UBRS and LBRS icons (same icon for both currently)
- Update BRD icon with new image
- Added event templates -- new events will default to the same values as the
last event you saved of the same type
- Fixed initial setup bugs
- Adjusted message delays
- Fixed bug which caused multiple people to respond to the same calendar
update requests
- Increased message delays to reduce traffic further

v1.0b3 Changes:

- Fixed error when trying to expand an empty attendance list
- Modified automatic channel configuration so that the channel
name/password/player are not stored in settings and displayed values are
taken from the current channel config
- Modified automatic channel config to update whenever a guild update occurs
- Modified channel config UI to update whenever a channel config change
occurs
- Fixed bug in event viewer close box which cause the schedule window to
stay open
- Added <self> will attend so users can include themselves in their own
events
- Started localization support
- Fixed error when creating a new event
- Added dungeon icons for Stratholme, Blackfathom Deeps, Maraudon, Ragefire
Chasm, Shadowfang Keep, Razorfen Downs, Razorfen Krawl, Wailing Caverns,
Scarlet Monastery and Uldaman
- Fixed error message caused when saving an edited event without viewing the
attendance
- Fixed event editor to show blank event type for new events
- Sharpened all the dungeon icons so they don't look so fuzzy

v1.0b2 Changes:

- Added sounds to tab-switching
- Updated version string to 1.0b2
- Modified removed trusted/excluded player to clear the selection after
removal
- Modified Channel setup to only enable Apply Changes button when something
is actually changed
- Fixed display of birthday events to not show attendance status
- Fixed bug causing only the last attendance value to be remembered

v1.0b1 Changes

- Added delete event confirmation
- Finished UI for event attendance

v1.0a12 Changes

- Added class and level info to acceptance messages
- Implemented more of the acceptance UI (still incomplete though)

v1.0a11 Changes

- Eliminated trust change notices when settings aren't actually changed
- Fixed selected tab rendering when showing calendar window
- Increased space allocated for channel panel description to avoid cropping
problems
- Added tooltips for channel tab controls
- Modified behavior to hide the event viewer/editor when switching to the
channel or trust tabs
- Added support for routing requests to all channel members
- Fixed bug which caused trust group to keep reverting to "only those listed
below"
- Modified auto config checkbox to be disabled when player isn't in a guild
- Corrected trust group dropdown to hide guildies option when not in a guild
(was hiding all option instead)
- Modified trust to solicit updates from guildies or the channel when
settings are changed
- Fixed default settings for guilded players
- Added attendance support to database engine
- Started on attendance UI

v1.0a10 Changes

- Added push-to-show icon to minimap (where the sun/moon icon normally is)
- Added enhanced time/date tooltip to minimap
- Actually tested the code first! Woot!
- Fixed trust caching issues when changing trust settings
- Added support for hiding the calendar window using the ESC key
- Added support for stacking the calendar window with other game windows
- Fixed initial synchronization problems
- Removed unwanted debug message

v1.0a9 Changes

- Removed unwanted debug message regarding user trust
- Corrected guild rank trust setting

v1.0a8 Changes

- Security (trust) implemented
- New settings/database formats OLD DATA AND SETTINGS WILL BE ERASED

v1.0a7 Changes

- Quick fix for a minor bug which caused periodic errors

v1.0a6 Changes

- Added Channel Setup page
- Added channel auto-configuration support
- Reduced network traffic caused by multiple users transmitting the same
updates
- Started on Trust Setup page (not hooked up)

v1.0a5 Changes

- Added calendar setup pages (not hooked up)
- Fixed database event duplication bug
- Added database integrity check at startup

 1.0a4 Changes

- Added meeting icon
- Modified event viewer to hide attendance feedback fields for
birthday events
- Added comment field to attendance feedback
- Modified event editor to show default title based on event type
- Fixed sorting for birthday events (previously they had a time of
7pm), existing birthday events will have to be edited in order to
correct them
- Changed time display for birthday events to simply show "Birthday"
instead of a time

1.0a3 Changes

- Added birthday icon
- Added icon overlay support to calendar display
- Added guild-specific update request support (no UI yet)

1.0a2 Changes

- Fixed communication protocol bugs

1.0a1 First public alpha release
