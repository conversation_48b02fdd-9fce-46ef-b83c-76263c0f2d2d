<UI xmlns="http://www.blizzard.com/wow/ui/"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.blizzard.com/wow/ui/FrameXML/UI.xsd">
	
	<Frame name="CalendarTimeTemplate" hidden="false" virtual="true">
		<Size>
			<AbsDimension x="150" y="21"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cTimeLabel" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="50" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString text=":" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="CENTER" relativePoint="RIGHT" relativeTo="$parentLabel">
							<Offset>
								<AbsDimension x="64" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentHour" inherits="CalendarDropDownTemplate">
				<Size>
					<AbsDimension x="45" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentLabel">
						<Offset>
							<AbsDimension x="-15" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarHourDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarHourDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
			<Frame name="$parentMinute" inherits="CalendarDropDownTemplate">
				<Size>
					<AbsDimension x="43" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentHour">
						<Offset>
							<AbsDimension x="-29" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarMinuteDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarMinuteDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
			<Frame name="$parentAMPM" inherits="CalendarDropDownTemplate">
				<Size>
					<AbsDimension x="48" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentMinute">
						<Offset>
							<AbsDimension x="-32" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarAMPMDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarAMPMDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>

	<Frame name="CalendarDurationTemplate" hidden="false" virtual="true">
		<Size>
			<AbsDimension x="150" y="21"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cDurationLabel" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="50" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentDuration" inherits="CalendarDropDownTemplate">
				<Size>
					<AbsDimension x="100" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentLabel">
						<Offset>
							<AbsDimension x="-15" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarDurationDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarDurationDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>

	<Frame name="CalendarEventTypeTemplate" hidden="false" virtual="true">
		<Size>
			<AbsDimension x="150" y="21"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cEventLabel" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="50" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentEventType" inherits="CalendarDropDownTemplate">
				<Size>
					<AbsDimension x="100" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentLabel">
						<Offset>
							<AbsDimension x="-15" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarEventTypeDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarEventTypeDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>

	<Frame name="CalendarPlayerClassMenuTemplate" virtual="true">
		<Size>
			<AbsDimension x="100" y="20"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cPlayerClassLabel" inherits="GameFontNormalSmall" justifyH="RIGHT">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="-12" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentMenu" inherits="CalendarDropDownTemplate">
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="-25" y="-2"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarClassDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarClassDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>

	<Frame name="CalendarPlayerRaceMenuTemplate" hidden="false" virtual="true">
		<Size>
			<AbsDimension x="100" y="20"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cPlayerRaceLabel" inherits="GameFontNormalSmall" justifyH="RIGHT">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="-12" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentMenu" inherits="CalendarDropDownTemplate">
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="-25" y="-2"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarRaceDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarRaceDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>

	<Frame name="CalendarPlayerStatusMenuTemplate" hidden="false" virtual="true">
		<Size>
			<AbsDimension x="100" y="20"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cPlayerStatusLabel" inherits="GameFontNormalSmall" justifyH="RIGHT">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="-45" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentMenu" inherits="CalendarDropDownTemplate">
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="-57" y="-2"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarStatusDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarStatusDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>
	<Frame name="CalendarPlayerRankMenuTemplate" hidden="false" virtual="true">
		<Size>
			<AbsDimension x="100" y="20"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cRankLabel" inherits="GameFontNormalSmall" justifyH="RIGHT">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="0" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentMenu" inherits="CalendarDropDownTemplate">
				<Anchors>
					<Anchor point="LEFT">
						<Offset>
							<AbsDimension x="-12" y="-2"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarGuildRank_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarGuildRank_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>
	
	<Frame name="CalendarCharactersTemplate" virtual="true">
		<Size>
			<AbsDimension x="150" y="21"/>
		</Size>
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentLabel" text="GroupCalendar_cCharactersLabel" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="RIGHT" relativePoint="LEFT">
							<Offset>
								<AbsDimension x="50" y="0"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="$parentMenu" inherits="CalendarDropDownTemplate">
				<Size>
					<AbsDimension x="100" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="$parentLabel">
						<Offset>
							<AbsDimension x="-15" y="-3"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						CalendarCharactersDropDown_OnLoad();
					</OnLoad>
					<OnShow>
						CalendarCharactersDropDown_OnLoad();
					</OnShow>
				</Scripts>
			</Frame>
		</Frames>
	</Frame>
	
</UI>
