--[[
    副本日历主文件 (RaidCalendar)
    ========================================
    
    功能说明：
    - 魔兽世界乌龟服副本重置日历
    - 支持周重置、5天重置、3天重置副本
    - 战场轮换日历显示
    - 模块化架构设计
    
    支持的副本重置周期：
    1. 周重置 (每周三12:00)
       - 熔火之心、黑翼之巢、安其拉神殿
       - 纳克萨玛斯、翡翠圣地、卡拉赞之塔
    
    2. 5天重置
       - 奥妮克希亚的巢穴、卡拉赞
    
    3. 3天重置
       - 祖尔格拉布、安其拉废墟
    
    主要组件：
    1. 日历界面
       - 月份导航和日期显示
       - 副本重置日期标识
       - 战场轮换背景显示
       - 实时服务器时间
    
    2. 小地图按钮
       - 左键：打开/关闭日历
       - 右键：打开/关闭副本记录
       - 鼠标悬停提示
    
    3. 独立右侧窗口
       - 副本记录模块 (RaidRecords)
       - 专业技能模块 (ProfessionCooldowns)
       - 可独立移动和关闭
       - 智能窗口大小调整
    
    4. 模块化架构
       - 副本记录数据同步
       - 专业技能冷却管理
       - 白名单角色验证
    
    战场轮换周期：
    - 阳光林地山谷 → 奥特兰克山谷 → 战歌峡谷 → 阿拉希盆地 → 血环竞技场
    - 每日凌晨3:00更新
    
    斜杠命令：
    - /rc - 打开/关闭主界面
    
    数据同步：
    - 消息前缀：RAIDCALSYNC
    - 支持工会、小队、团队频道
    - 白名单角色验证机制
    
    版本信息：
    - 当前版本：2.0.0
    - 作者：拉风乌龟
    - 适用服务器：乌龟服 (Turtle WoW)
    - 客户端版本：魔兽世界1.12.1
    
    模块依赖：
    - RaidRecords.lua - 副本记录模块
    - ProfessionCooldowns.lua - 专业技能模块
    - Whitelist.lua - 白名单配置
    
    安装说明：
    1. 将整个RaidCalendar文件夹放入AddOns目录
    2. 重启游戏或重载UI (/reload)
    3. 使用 /rc 命令或点击小地图按钮开始使用
--]]

RaidCalendar = {}
if DEFAULT_CHAT_FRAME then
end
RaidCalendar.version = "3.0.0"

local currentDate = date("*t")
RaidCalendar.currentYear = currentDate.year
RaidCalendar.currentMonth = currentDate.month
RaidCalendar.minimapButton = nil
RaidCalendar.fiveDaySystemInitialized = false
RaidCalendar.threeDaySystemInitialized = false
RaidCalendar.MSG_PREFIX = "RAIDCALSYNC"
RaidCalendar.rightFrameVisible = false  -- 新增：保存右侧窗口状态

RaidCalendar.ClassColors = {
    ["WARRIOR"] = { r = 0.78, g = 0.61, b = 0.43 },
    ["PALADIN"] = { r = 0.96, g = 0.55, b = 0.73 },
    ["HUNTER"] = { r = 0.67, g = 0.83, b = 0.45 },
    ["ROGUE"] = { r = 1.00, g = 0.96, b = 0.41 },
    ["PRIEST"] = { r = 1.00, g = 1.00, b = 1.00 },
    ["SHAMAN"] = { r = 0.00, g = 0.44, b = 0.87 },
    ["MAGE"] = { r = 0.25, g = 0.78, b = 0.92 },
    ["WARLOCK"] = { r = 0.53, g = 0.53, b = 0.93 },
    ["DRUID"] = { r = 1.00, g = 0.49, b = 0.04 },
}

function RaidCalendar_GetClassColorHex(classToken)
    if classToken and RaidCalendar.ClassColors[classToken] then
        local colorInfo = RaidCalendar.ClassColors[classToken]
        return string.format("%02x%02x%02x", colorInfo.r * 255, colorInfo.g * 255, colorInfo.b * 255)
    end
    return "FFFFFF"
end

function RaidCalendar_UpdateMonthYearDisplay()
    if RaidCalendar.monthYearText then
        RaidCalendar.monthYearText:SetText(string.format("%d年%02d月", RaidCalendar.currentYear, RaidCalendar.currentMonth))
    end
end

function RaidCalendar_PrevMonth()
    RaidCalendar.currentMonth = RaidCalendar.currentMonth - 1
    if RaidCalendar.currentMonth < 1 then
        RaidCalendar.currentMonth = 12
        RaidCalendar.currentYear = RaidCalendar.currentYear - 1
    end
    RaidCalendar_UpdateMonthYearDisplay()
    RaidCalendar_RefreshUI()
end

function RaidCalendar_NextMonth()
    RaidCalendar.currentMonth = RaidCalendar.currentMonth + 1
    if RaidCalendar.currentMonth > 12 then
        RaidCalendar.currentMonth = 1
        RaidCalendar.currentYear = RaidCalendar.currentYear + 1
    end
    RaidCalendar_UpdateMonthYearDisplay()
    RaidCalendar_RefreshUI()
end

function RaidCalendar_SlashHandler(msg)
    msg = msg or ""
    
    if RaidCalendarMainFrame then
        if RaidCalendarMainFrame:IsVisible() then
            RaidCalendarMainFrame:Hide()
        else
            local currentDate = date("*t")
            RaidCalendar.currentYear = currentDate.year
            RaidCalendar.currentMonth = currentDate.month
            
            RaidCalendarMainFrame:Show()
            
            -- 恢复右侧窗口状态
            if RaidCalendar.rightFrameVisible then
                RaidCalendar.rightFrame:Show()
                -- 只在首次显示时刷新右侧面板内容
                if type(RaidCalendar_UpdateRaidInfo) == "function" then
                    RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
                end
                if type(RaidCalendar_RefreshProfessionData) == "function" then
                    RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
                end
            end
            
            RaidCalendar_UpdateMonthYearDisplay()
            RaidCalendar_RefreshUI()
        end
    else
        DEFAULT_CHAT_FRAME:AddMessage("副本日历: 界面未加载")
    end
end

function RaidCalendar_ShowResetInfo()
    local currentTime = time()
    local timeTable = date("*t", currentTime)
    local wday = timeTable.wday
    local daysToReset = 0
    
    if wday < 4 then
        daysToReset = 4 - wday
    elseif wday == 4 then
        if timeTable.hour >= 12 then
            daysToReset = 7
        else
            daysToReset = 0
        end
    else
        daysToReset = 7 - wday + 4
    end
    
    local resetTime = currentTime + (daysToReset * 24 * 3600)
    local resetTable = date("*t", resetTime)
    
    DEFAULT_CHAT_FRAME:AddMessage("副本日历: 下次团队副本重置")
    DEFAULT_CHAT_FRAME:AddMessage(string.format("时间: %d月%d日 12:00", resetTable.month, resetTable.day))
    
    if daysToReset > 0 then
        DEFAULT_CHAT_FRAME:AddMessage(string.format("倒计时: %d天", daysToReset))
    else
        local hoursToReset = 12 - timeTable.hour
        if hoursToReset > 0 then
            DEFAULT_CHAT_FRAME:AddMessage(string.format("倒计时: %d小时", hoursToReset))
        else
            DEFAULT_CHAT_FRAME:AddMessage("今天已重置")
        end
    end
end

RaidCalendar.dayButtons = {}

function RaidCalendar_OnLoad()
    this:RegisterForDrag("LeftButton")
    
    RaidCalendarMainFrame:SetFrameStrata("FULLSCREEN_DIALOG")
    RaidCalendarMainFrame:SetFrameLevel(100)
    
    -- 调整主界面为只显示日历（左侧区域）
    RaidCalendarMainFrame:SetWidth(625)
    RaidCalendarMainFrame:SetHeight(680)
    
    -- 主界面现在就是左侧面板（日历面板）
    local leftPanel = RaidCalendarMainFrame
    leftPanel:SetWidth(625)
    leftPanel:SetHeight(680)
    
    -- 左侧背景设置 - 与右侧完全一致
    local leftBgBack = leftPanel:CreateTexture(nil, "BACKGROUND")
    leftBgBack:SetAllPoints(leftPanel)
    leftBgBack:SetTexture("Interface\\DialogFrame\\UI-DialogBox-Background")
    
    local leftBg = leftPanel:CreateTexture(nil, "ARTWORK")
    leftBg:SetAllPoints(leftPanel)
    leftBg:SetTexture("Interface\\FrameGeneral\\UI-Background-Rock")
    leftBg:SetAlpha(1.0)
    
    -- 创建独立的右侧窗口（副本记录和专业技能窗口）
    RaidCalendar.rightFrame = CreateFrame("Frame", "RaidCalendarRightFrame", UIParent)
    -- 默认锚点设置为屏幕中央，这样即使主界面没开也能显示
    RaidCalendar.rightFrame:SetPoint("CENTER", UIParent, "CENTER", 200, 0)
    RaidCalendar.rightFrame:SetWidth(400)  -- 默认宽度
    RaidCalendar.rightFrame:SetHeight(680)
    RaidCalendar.rightFrame:SetFrameStrata("FULLSCREEN_DIALOG")
    RaidCalendar.rightFrame:SetFrameLevel(100)
    RaidCalendar.rightFrame:EnableMouse(true)
    RaidCalendar.rightFrame:SetMovable(true)
    RaidCalendar.rightFrame:RegisterForDrag("LeftButton")
    RaidCalendar.rightFrame:SetScript("OnDragStart", function() this:StartMoving() end)
    RaidCalendar.rightFrame:SetScript("OnDragStop", function() this:StopMovingOrSizing() end)
    RaidCalendar.rightFrame:Hide() -- 默认隐藏
    
    -- 右侧窗口背景（使用与左侧完全相同的风格）
    local rightBgBack = RaidCalendar.rightFrame:CreateTexture(nil, "BACKGROUND")
    rightBgBack:SetAllPoints(RaidCalendar.rightFrame)
    rightBgBack:SetTexture("Interface\\DialogFrame\\UI-DialogBox-Background")
    
    local rightArtwork = RaidCalendar.rightFrame:CreateTexture(nil, "ARTWORK")
    rightArtwork:SetAllPoints(RaidCalendar.rightFrame)
    rightArtwork:SetTexture("Interface\\FrameGeneral\\UI-Background-Rock")
    rightArtwork:SetAlpha(1.0)
    
    -- 右侧窗口关闭按钮
    local closeButton = CreateFrame("Button", "RaidCalendarRightCloseButton", RaidCalendar.rightFrame, "UIPanelCloseButton")
    closeButton:SetPoint("TOPRIGHT", RaidCalendar.rightFrame, "TOPRIGHT", -5, -5)
    closeButton:SetScript("OnClick", function()
        RaidCalendar.rightFrame:Hide()
        RaidCalendar.rightFrameVisible = false  -- 保存状态
    end)
    
    -- 将原来的 rightPanel 设置为新的独立窗口的内容区域
    RaidCalendar.rightPanel = RaidCalendar.rightFrame
    
    local leftTitle = leftPanel:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    leftTitle:SetPoint("TOP", leftPanel, "TOP", 0, -20)  -- 调整到居中位置，x坐标从-25改为0
    leftTitle:SetText("副本日历")
    leftTitle:SetTextColor(1, 1, 0)

    -- 修改为与关闭按钮大小一致的展开按钮
    local expandButton = CreateFrame("Button", "RaidCalendarExpandButton", leftPanel)
    expandButton:SetPoint("TOPRIGHT", leftPanel, "TOPRIGHT", -15, -80)  -- 从-30改为-15，向右移动15像素
    expandButton:SetWidth(25)
    expandButton:SetHeight(25)
    
    -- 展开箭头图标（去掉背景和边框）
    local expandIcon = expandButton:CreateTexture(nil, "OVERLAY")
    expandIcon:SetAllPoints(expandButton)
    expandIcon:SetTexture("Interface\\Buttons\\UI-SpellbookIcon-NextPage-Up")
    
    -- 按钮功能
    expandButton:SetScript("OnClick", function()
        if RaidCalendar.rightFrame:IsVisible() then
            RaidCalendar.rightFrame:Hide()
            RaidCalendar.rightFrameVisible = false  -- 保存状态
            expandIcon:SetTexture("Interface\\Buttons\\UI-SpellbookIcon-NextPage-Up")
        else
            RaidCalendar.rightFrame:Show()
            RaidCalendar.rightFrameVisible = true   -- 保存状态
            expandIcon:SetTexture("Interface\\Buttons\\UI-SpellbookIcon-NextPage-Down")
            -- 只在首次显示时刷新右侧面板内容
            if type(RaidCalendar_UpdateRaidInfo) == "function" then
                RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
            end
            if type(RaidCalendar_RefreshProfessionData) == "function" then
                RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
            end
        end
    end)
    
    -- 鼠标悬停提示
    expandButton:SetScript("OnEnter", function()
        GameTooltip:SetOwner(this, "ANCHOR_LEFT")
        if RaidCalendar.rightFrame:IsVisible() then
            GameTooltip:SetText("隐藏副本记录", 1, 1, 1)
        else
            GameTooltip:SetText("显示副本记录", 1, 1, 1)
        end
        GameTooltip:Show()
    end)
    
    expandButton:SetScript("OnLeave", function()
        GameTooltip:Hide()
    end)

    local timeFrame = CreateFrame("Frame", "RaidCalendarTimeFrame", leftPanel)
    timeFrame:SetPoint("TOPLEFT", leftPanel, "TOPLEFT", 60, -40)  -- 向左移动20像素
    timeFrame:SetWidth(200)
    timeFrame:SetHeight(50)

    timeFrame:SetWidth(200)
    timeFrame:SetHeight(50)
    
    local serverTime = timeFrame:CreateFontString(nil, "OVERLAY", "GameFontHighlightLarge")
    serverTime:SetPoint("TOP", timeFrame, "TOP", -20, 0)
    serverTime:SetText("16:03:52")
    serverTime:SetTextColor(1, 1, 1)
    
    local dateText = timeFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    dateText:SetPoint("TOP", timeFrame, "TOP", -20, -20)
    dateText:SetText("2020年04月27日 星期一")
    dateText:SetTextColor(0.6, 0.8, 1)

    local timeUpdate = CreateFrame("Frame")
    timeUpdate:SetScript("OnUpdate", function()
        local currentTime = time()
        local timeTable = date("*t", currentTime)
        
        local timeStr = string.format("%02d:%02d:%02d", timeTable.hour, timeTable.min, timeTable.sec)
        serverTime:SetText(timeStr)
        
        local weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"}
        local dateStr = string.format("%d年%02d月%02d日 %s", timeTable.year, timeTable.month, timeTable.day, weekDays[timeTable.wday])
        dateText:SetText(dateStr)
    end)
    
    local weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"}
    local startX = 15  -- 从35改为15，向左移动20像素

    for i = 1, 7 do
        local weekFrame = CreateFrame("Frame", "RaidCalendarWeek" .. i, leftPanel)
        weekFrame:SetPoint("TOPLEFT", leftPanel, "TOPLEFT", startX + ((i-1) * 85), -110)
        weekFrame:SetWidth(83)
        weekFrame:SetHeight(33)
        weekFrame:SetBackdrop({
          edgeFile = "Interface\\Buttons\\WHITE8X8",
          edgeSize = 1,
        })
        weekFrame:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.3)
        weekFrame:SetBackdropColor(0, 0, 0, 0)
        
        local weekBg = weekFrame:CreateTexture(nil, "BACKGROUND")
        weekBg:SetAllPoints(weekFrame)
        weekBg:SetTexture("Interface\\Common\\Common-ScrollBox-BG")
        weekBg:SetAlpha(0.7)
        
        local weekLabel = weekFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        weekLabel:SetPoint("CENTER", weekFrame, "CENTER", 0, 0)
        weekLabel:SetText(weekDays[i])
        weekLabel:SetTextColor(1, 1, 0)
    end

    RaidCalendar.monthYearText = leftPanel:CreateFontString(nil, "OVERLAY", "GameFontNormalHuge")
    RaidCalendar.monthYearText:SetPoint("TOP", leftPanel, "TOP", -20, -50)  -- 向左移动20像素
    RaidCalendar.monthYearText:SetPoint("TOPLEFT", leftPanel, "TOPLEFT", 440 - (RaidCalendar.monthYearText:GetWidth() / 2), -50)  -- 向左移动20像素
    RaidCalendar.monthYearText:SetText("2025年06月")
    RaidCalendar.monthYearText:SetTextColor(1, 1, 1)

    local prevMonthButton = CreateFrame("Button", "RaidCalendarPrevMonthButton", leftPanel, "UIPanelButtonTemplate")
    prevMonthButton:SetPoint("RIGHT", RaidCalendar.monthYearText, "LEFT", -5, 0)
    prevMonthButton:SetWidth(15)
    prevMonthButton:SetHeight(15)
    prevMonthButton:SetText("<")
    prevMonthButton:SetScript("OnClick", RaidCalendar_PrevMonth)

    local nextMonthButton = CreateFrame("Button", "RaidCalendarNextMonthButton", leftPanel, "UIPanelButtonTemplate")
    nextMonthButton:SetPoint("LEFT", RaidCalendar.monthYearText, "RIGHT", 5, 0)
    nextMonthButton:SetWidth(15)
    nextMonthButton:SetHeight(15)
    nextMonthButton:SetText(">")
    nextMonthButton:SetScript("OnClick", RaidCalendar_NextMonth)

    -- 在右侧窗口添加作者和版本信息
    local authorText = RaidCalendar.rightFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
    authorText:SetPoint("BOTTOMRIGHT", RaidCalendar.rightFrame, "BOTTOMRIGHT", -15, 15)
    authorText:SetTextColor(0.8, 0.8, 1, 1)
    authorText:SetText("作者: 拉风乌龟")

    local versionText = RaidCalendar.rightFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
    versionText:SetPoint("BOTTOMRIGHT", authorText, "TOP", 17, 5)  -- 从15改为17，向右移动2像素
    versionText:SetTextColor(0.8, 0.8, 1, 1)
    versionText:SetText("版本: " .. RaidCalendar.version)

end

function RaidCalendar_GenerateCalendar()
    RaidCalendar_ClearDayButtons()
    
    local currentTable = {year = RaidCalendar.currentYear, month = RaidCalendar.currentMonth, day = 1}
    
    local firstDay = time({
        year = RaidCalendar.currentYear,
        month = RaidCalendar.currentMonth,
        day = 1,
        hour = 0,
        min = 0,
        sec = 0
    })
    local firstDayWday = date("*t", firstDay).wday
    
    local nextMonth = RaidCalendar.currentMonth + 1
    local nextYear = RaidCalendar.currentYear
    if nextMonth > 12 then
        nextMonth = 1
        nextYear = nextYear + 1
    end
    local nextMonthFirst = time({
        year = nextYear,
        month = nextMonth,
        day = 1,
        hour = 0,
        min = 0,
        sec = 0
    })
    local daysInMonth = math.floor((nextMonthFirst - firstDay) / 86400)
    
    local cellWidth = 85
    local cellHeight = 85
    local totalWidth = 7 * cellWidth
    local totalHeight = 5 * cellHeight
    
    local startX = 15
    local startY = -600 + totalHeight + 30
    
    local dayCount = 1
    local row = 0
    local col = firstDayWday - 1
    
    local prevMonthDaysToPad = firstDayWday - 1
    local prevMonthYear = RaidCalendar.currentYear
    local prevMonth = RaidCalendar.currentMonth - 1
    if prevMonth < 1 then
        prevMonth = 12
        prevMonthYear = prevMonthYear - 1
    end
    local prevMonthTime = time({year = prevMonthYear, month = prevMonth, day = 1, hour = 0, min = 0, sec = 0})
    local nextDayOfPrevMonth = time({year = RaidCalendar.currentYear, month = RaidCalendar.currentMonth, day = 1, hour = 0, min = 0, sec = 0})
    local daysInPrevMonth = math.floor((nextDayOfPrevMonth - prevMonthTime) / 86400)

    -- 获取真正的今天日期（只获取一次）
    local today_date = date("*t", time())

    for i = 1, prevMonthDaysToPad do
        local dayInPrevMonth = daysInPrevMonth - prevMonthDaysToPad + i
        local prevDayCol = i - 1
        local x = startX + (prevDayCol * cellWidth)
        local y = startY

        local prevDayButton = CreateFrame("Button", "RaidCalendarPrev" .. i, RaidCalendarMainFrame)
        prevDayButton:SetPoint("TOPLEFT", RaidCalendarMainFrame, "TOPLEFT", x, y)
        prevDayButton:SetWidth(cellWidth - 2)
        prevDayButton:SetHeight(cellHeight - 2)
        
        local isToday = (today_date.year == prevMonthYear and 
                         today_date.month == prevMonth and 
                         today_date.day == dayInPrevMonth)
        
        -- 所有按钮统一使用普通边框，不区分今日
        prevDayButton:SetBackdrop({
          edgeFile = "Interface\\Buttons\\WHITE8X8",
          edgeSize = 1,
        })
        prevDayButton:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.3)
        prevDayButton:SetBackdropColor(0, 0, 0, 0)

        local dayText = prevDayButton:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        dayText:SetPoint("TOPLEFT", prevDayButton, "TOPLEFT", 3, -3)
        dayText:SetText(prevMonth .. "-" .. dayInPrevMonth)
        dayText:SetTextColor(0.5, 0.5, 0.5)

        do
            local actualPrevDayTime = time({
                year = prevMonthYear,
                month = prevMonth,
                day = dayInPrevMonth,
                hour = 12,
                min = 0,
                sec = 0
            })
            local actualPrevDayWday = date("*t", actualPrevDayTime).wday

            -- 图标放置逻辑（包含钓鱼大赛）
            do
                local iconSize = 20
                local iconSpacing = 1
                local iconsPerRow = 4
                local currentIconX = 0
                local currentIconY = 0
                local iconsInCurrentRow = 0
                local iconsPlacedByPreviousCycle = false

                local function placeAndAdvanceIcon(button, texturePath)
                    if not texturePath then return end
                    local icon = button:CreateTexture(nil, "OVERLAY")
                    icon:SetPoint("BOTTOMLEFT", button, "BOTTOMLEFT", currentIconX, currentIconY)
                    icon:SetWidth(iconSize)
                    icon:SetHeight(iconSize)
                    icon:SetTexture(texturePath)
                    icon:SetAlpha(0.2)

                    currentIconX = currentIconX + iconSize + iconSpacing
                    iconsInCurrentRow = iconsInCurrentRow + 1
                    if iconsInCurrentRow >= iconsPerRow then
                        currentIconX = 0
                        currentIconY = currentIconY + iconSize + iconSpacing
                        iconsInCurrentRow = 0
                    end
                end

                -- 添加钓鱼大赛图标（每周日）
                if actualPrevDayWday == 1 then -- 周日
                    local fishingIcon = prevDayButton:CreateTexture(nil, "OVERLAY")
                    fishingIcon:SetPoint("TOPRIGHT", prevDayButton, "TOPRIGHT", 0, 0)
                    fishingIcon:SetWidth(20)
                    fishingIcon:SetHeight(20)
                    fishingIcon:SetTexture("Interface\\AddOns\\RaidCalendar\\textures\\DY_Icon.tga")
                    fishingIcon:SetAlpha(0.2)
                end

                if actualPrevDayWday == 4 then
                    for _, texturePath in ipairs(RaidCalendar.weeklyRaidIcons) do
                        placeAndAdvanceIcon(prevDayButton, texturePath)
                    end
                    iconsPlacedByPreviousCycle = true
                end

                if RaidCalendar_CheckFiveDayReset and RaidCalendar_CheckFiveDayReset(actualPrevDayTime) and RaidCalendar.fiveDayResetRaids then
                    if not iconsPlacedByPreviousCycle then
                        currentIconX = 0
                        currentIconY = 0
                        iconsInCurrentRow = 0
                    end
                    for _, raidInfo in ipairs(RaidCalendar.fiveDayResetRaids) do
                        placeAndAdvanceIcon(prevDayButton, raidInfo.icon)
                    end
                    iconsPlacedByPreviousCycle = true
                end

                if RaidCalendar_CheckThreeDayReset and RaidCalendar_CheckThreeDayReset(actualPrevDayTime) and RaidCalendar.threeDayResetRaids then
                    if not iconsPlacedByPreviousCycle then
                        currentIconX = 0
                        currentIconY = 0
                        iconsInCurrentRow = 0
                    end
                    for _, raidInfo in ipairs(RaidCalendar.threeDayResetRaids) do
                        placeAndAdvanceIcon(prevDayButton, raidInfo.icon)
                    end
                end
            end
        end

        -- 添加上个月的战场图片和鼠标提示
        do
            local idx = RaidCalendar_GetBattleIndexForDate(prevMonthYear, prevMonth, dayInPrevMonth)
            local battle = RaidCalendar.battleCycle[idx]
            if battle and battle.icon then
                local battleBg = prevDayButton:CreateTexture("PrevBattleBg_" .. i, "BACKGROUND")
                battleBg:SetPoint("CENTER", prevDayButton, "CENTER", 0, 0)
                battleBg:SetWidth(83)
                battleBg:SetHeight(83)
                battleBg:SetTexture(battle.icon)
                battleBg:SetAlpha(0.1)
                battleBg:SetDrawLayer("BACKGROUND", 1)
                
                prevDayButton.dayNumber = dayInPrevMonth
                prevDayButton.dayWday = actualPrevDayWday
                prevDayButton.monthNumber = prevMonth
                prevDayButton:SetScript("OnEnter", function()
                    RaidCalendar_ShowDayTooltip(this.dayNumber, this.dayWday, this.monthNumber)
                    GameTooltip:AddLine("战场："..battle.name, 1, 0.82, 0)
                    GameTooltip:AddLine("更新时间：凌晨3:00", 0.8, 0.8, 0.8)
                    GameTooltip:Show()
                end)
                prevDayButton:SetScript("OnLeave", function()
                    GameTooltip:Hide()
                end)
            end
        end

        table.insert(RaidCalendar.dayButtons, prevDayButton)
    end

    -- 显示本月的日期
    while dayCount <= daysInMonth do
        local x = startX + (col * cellWidth)
        local y = startY - (row * cellHeight)
        
        local dayButton = CreateFrame("Button", "RaidCalendarDay" .. dayCount, RaidCalendarMainFrame)
        dayButton:SetPoint("TOPLEFT", RaidCalendarMainFrame, "TOPLEFT", x, y)
        dayButton:SetWidth(cellWidth - 2)
        dayButton:SetHeight(cellHeight - 2)

        -- 修复：当月日期的今天判断
        local isToday = (today_date.year == RaidCalendar.currentYear and 
                         today_date.month == RaidCalendar.currentMonth and 
                         today_date.day == dayCount)
        
        -- 所有按钮统一使用普通边框，不区分今日
        dayButton:SetBackdrop({
          edgeFile = "Interface\\Buttons\\WHITE8X8",
          edgeSize = 1,
        })
        dayButton:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.3)
        dayButton:SetBackdropColor(0, 0, 0, 0)

        local dayText = dayButton:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        dayText:SetPoint("TOPLEFT", dayButton, "TOPLEFT", 3, -3)
        dayText:SetText(RaidCalendar.currentMonth .. "-" .. dayCount .. "  ")  -- 月-日格式
        dayText:SetTextColor(1, 1, 1) -- 默认白色

        -- 如果是今天，则放大日期文字并改变颜色
        if isToday then
            dayText:SetFontObject(GameFontNormalLarge)
            dayText:SetTextColor(1, 1, 0, 1.0)
        end
        
        local dayTime = time({
            year = currentTable.year,
            month = currentTable.month,
            day = dayCount,
            hour = 12,
            min = 0,
            sec = 0
        })
        local dayWday = date("*t", dayTime).wday
        
        -- 图标放置逻辑（包含钓鱼大赛）
        do
            local iconSize = 20
            local iconSpacing = 1
            local iconsPerRow = 4
            local currentIconX = 0
            local currentIconY = 0
            local iconsInCurrentRow = 0
            local iconsPlacedByPreviousCycle = false

            local function placeAndAdvanceIcon(button, texturePath)
                if not texturePath then return end
                local icon = button:CreateTexture(nil, "OVERLAY")
                icon:SetPoint("BOTTOMLEFT", button, "BOTTOMLEFT", currentIconX, currentIconY)
                icon:SetWidth(iconSize)
                icon:SetHeight(iconSize)
                icon:SetTexture(texturePath)
                icon:SetAlpha(1.0)

                currentIconX = currentIconX + iconSize + iconSpacing
                iconsInCurrentRow = iconsInCurrentRow + 1
                if iconsInCurrentRow >= iconsPerRow then
                    currentIconX = 0
                    currentIconY = currentIconY + iconSize + iconSpacing
                    iconsInCurrentRow = 0
                end
            end

            -- 添加钓鱼大赛图标（每周日）
            if dayWday == 1 then -- 周日
                local fishingIcon = dayButton:CreateTexture(nil, "OVERLAY")
                fishingIcon:SetPoint("TOPRIGHT", dayButton, "TOPRIGHT", 0, 0)
                fishingIcon:SetWidth(20)
                fishingIcon:SetHeight(20)
                fishingIcon:SetTexture("Interface\\AddOns\\RaidCalendar\\textures\\DY_Icon.tga")
                fishingIcon:SetAlpha(1.0)
            end

            if dayWday == 4 then
                for _, texturePath in ipairs(RaidCalendar.weeklyRaidIcons) do
                    placeAndAdvanceIcon(dayButton, texturePath)
                end
                iconsPlacedByPreviousCycle = true
            end

            if RaidCalendar_CheckFiveDayReset and RaidCalendar_CheckFiveDayReset(dayTime) and RaidCalendar.fiveDayResetRaids then
                if not iconsPlacedByPreviousCycle then
                    currentIconX = 0
                    currentIconY = 0
                    iconsInCurrentRow = 0
                end
                for _, raidInfo in ipairs(RaidCalendar.fiveDayResetRaids) do
                    placeAndAdvanceIcon(dayButton, raidInfo.icon)
                end
                iconsPlacedByPreviousCycle = true
            end

            if RaidCalendar_CheckThreeDayReset and RaidCalendar_CheckThreeDayReset(dayTime) and RaidCalendar.threeDayResetRaids then
                if not iconsPlacedByPreviousCycle then
                    currentIconX = 0
                    currentIconY = 0
                    iconsInCurrentRow = 0
                end
                for _, raidInfo in ipairs(RaidCalendar.threeDayResetRaids) do
                    placeAndAdvanceIcon(dayButton, raidInfo.icon)
                end
            end
        end
        
        dayButton.dayNumber = dayCount
        dayButton.dayWday = dayWday
        dayButton.monthNumber = currentTable.month
        
        -- 添加当月战场图片
        do
            local idx = RaidCalendar_GetBattleIndexForDate(RaidCalendar.currentYear, currentTable.month, dayCount)
            local battle = RaidCalendar.battleCycle[idx]
            if battle and battle.icon then
                local battleBg = dayButton:CreateTexture("BattleBg_" .. dayCount, "BACKGROUND")
                battleBg:SetPoint("CENTER", dayButton, "CENTER", 0, 0)
                battleBg:SetWidth(83)
                battleBg:SetHeight(83)
                battleBg:SetTexture(battle.icon)
                battleBg:SetAlpha(0.8) -- 从1.0改为0.9，降低透明度
                battleBg:SetDrawLayer("BACKGROUND", 1)
                
                dayButton:SetScript("OnEnter", function()
                    RaidCalendar_ShowDayTooltip(this.dayNumber, this.dayWday, this.monthNumber)
                    GameTooltip:AddLine("战场："..battle.name, 1, 0.82, 0)
                    GameTooltip:AddLine("更新时间：凌晨3:00", 0.8, 0.8, 0.8)
                    GameTooltip:Show()
                end)
            else
                dayButton:SetScript("OnEnter", function()
                    RaidCalendar_ShowDayTooltip(this.dayNumber, this.dayWday, this.monthNumber)
                end)
            end
        end
        
        -- 如果是今天，创建独立的最上层边框
        if isToday then
            local borderFrame = CreateFrame("Frame", nil, RaidCalendarMainFrame)
            borderFrame:SetAllPoints(dayButton)
            borderFrame:SetFrameLevel(dayButton:GetFrameLevel() + 20)
            borderFrame:SetBackdrop({
              edgeFile = "Interface\\Buttons\\WHITE8X8",
              edgeSize = 3,
            })
            borderFrame:SetBackdropBorderColor(1, 1, 0, 1.0)
            borderFrame:SetBackdropColor(0, 0, 0, 0)
            
            -- 将边框Frame加入到dayButtons数组中，这样清理时会被一起清理
            table.insert(RaidCalendar.dayButtons, borderFrame)
        end

        dayButton:SetScript("OnLeave", function()
            GameTooltip:Hide()
        end)

        table.insert(RaidCalendar.dayButtons, dayButton)
        
        dayCount = dayCount + 1
        col = col + 1
        
        if col >= 7 then
            col = 0
            row = row + 1
        end
    end
    
    if col > 0 then
        local nextDayCount = 1
        while col < 7 do
            local x = startX + (col * cellWidth)
            local y = startY - (row * cellHeight)
            
            local nextDayButton = CreateFrame("Button", "RaidCalendarNext" .. nextDayCount, RaidCalendarMainFrame)
            nextDayButton:SetPoint("TOPLEFT", RaidCalendarMainFrame, "TOPLEFT", x, y)
            nextDayButton:SetWidth(cellWidth - 2)
            nextDayButton:SetHeight(cellHeight - 2)
            
            -- 修复：下个月的日期不应该被高亮为今天
            local isToday = (today_date.year == nextYear and 
                             today_date.month == nextMonth and 
                             today_date.day == nextDayCount)
            
            -- 所有按钮统一使用普通边框，不区分今日
            nextDayButton:SetBackdrop({
              edgeFile = "Interface\\Buttons\\WHITE8X8",
              edgeSize = 1,
            })
            nextDayButton:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.3)
            nextDayButton:SetBackdropColor(0, 0, 0, 0)

            local nextDayText = nextDayButton:CreateFontString(nil, "OVERLAY", "GameFontNormal")
            nextDayText:SetPoint("TOPLEFT", nextDayButton, "TOPLEFT", 3, -3)
            nextDayText:SetText(nextMonth .. "-" .. nextDayCount)
            nextDayText:SetTextColor(0.5, 0.5, 0.5)

            local nextDayTime = time({
                year = nextYear,
                month = nextMonth,
                day = nextDayCount,
                hour = 12,
                min = 0,
                sec = 0
            })
            local nextDayWday = date("*t", nextDayTime).wday

            -- 图标放置逻辑（包含钓鱼大赛）
            do
                local iconSize = 20
                local iconSpacing = 1
                local iconsPerRow = 4
                local currentIconX = 0
                local currentIconY = 0
                local iconsInCurrentRow = 0
                local iconsPlacedByPreviousCycle = false

                local function placeAndAdvanceIcon(button, texturePath)
                    if not texturePath then return end
                    local icon = button:CreateTexture(nil, "OVERLAY")
                    icon:SetPoint("BOTTOMLEFT", button, "BOTTOMLEFT", currentIconX, currentIconY)
                    icon:SetWidth(iconSize)
                    icon:SetHeight(iconSize)
                    icon:SetTexture(texturePath)
                    icon:SetAlpha(0.2)

                    currentIconX = currentIconX + iconSize + iconSpacing
                    iconsInCurrentRow = iconsInCurrentRow + 1
                    if iconsInCurrentRow >= iconsPerRow then
                    currentIconX = 0
                    currentIconY = currentIconY + iconSize + iconSpacing
                    iconsInCurrentRow = 0
                end
                end

                -- 添加钓鱼大赛图标（每周日）
                if nextDayWday == 1 then -- 周日
                    local fishingIcon = nextDayButton:CreateTexture(nil, "OVERLAY")
                    fishingIcon:SetPoint("TOPRIGHT", nextDayButton, "TOPRIGHT", 0, 0)
                    fishingIcon:SetWidth(20)
                    fishingIcon:SetHeight(20)
                    fishingIcon:SetTexture("Interface\\AddOns\\RaidCalendar\\textures\\DY_Icon.tga")
                    fishingIcon:SetAlpha(0.2)
                end

                if nextDayWday == 4 then
                    for _, texturePath in ipairs(RaidCalendar.weeklyRaidIcons) do
                        placeAndAdvanceIcon(nextDayButton, texturePath)
                    end
                    iconsPlacedByPreviousCycle = true
                end

                if RaidCalendar_CheckFiveDayReset and RaidCalendar_CheckFiveDayReset(nextDayTime) and RaidCalendar.fiveDayResetRaids then
                    if not iconsPlacedByPreviousCycle then
                        currentIconX = 0
                        currentIconY = 0
                        iconsInCurrentRow = 0
                    end
                    for _, raidInfo in ipairs(RaidCalendar.fiveDayResetRaids) do
                        placeAndAdvanceIcon(nextDayButton, raidInfo.icon)
                    end
                    iconsPlacedByPreviousCycle = true
                end

                if RaidCalendar_CheckThreeDayReset and RaidCalendar_CheckThreeDayReset(nextDayTime) and RaidCalendar.threeDayResetRaids then
                    if not iconsPlacedByPreviousCycle then
                        currentIconX = 0
                        currentIconY = 0
                        iconsInCurrentRow = 0
                    end
                    for _, raidInfo in ipairs(RaidCalendar.threeDayResetRaids) do
                        placeAndAdvanceIcon(nextDayButton, raidInfo.icon)
                    end
                end
            end
            
            -- 添加下个月的战场图片和鼠标提示
            do
                local idx = RaidCalendar_GetBattleIndexForDate(nextYear, nextMonth, nextDayCount)
                local battle = RaidCalendar.battleCycle[idx]
                if battle and battle.icon then
                    local battleBg = nextDayButton:CreateTexture("NextBattleBg_" .. nextDayCount, "BACKGROUND")
                    battleBg:SetPoint("CENTER", nextDayButton, "CENTER", 0, 0)
                    battleBg:SetWidth(83)
                    battleBg:SetHeight(83)
                    battleBg:SetTexture(battle.icon)
                    battleBg:SetAlpha(0.1)
                    battleBg:SetDrawLayer("BACKGROUND", 1)
                    
                    nextDayButton.dayNumber = nextDayCount
                    nextDayButton.dayWday = nextDayWday
                    nextDayButton.monthNumber = nextMonth
                    nextDayButton:SetScript("OnEnter", function()
                        RaidCalendar_ShowDayTooltip(this.dayNumber, this.dayWday, this.monthNumber)
                        GameTooltip:AddLine("战场："..battle.name, 1, 0.82, 0)
                        GameTooltip:AddLine("更新时间：凌晨3:00", 0.8, 0.8, 0.8)
                        GameTooltip:Show()
                    end)
                else
                    nextDayButton.dayNumber = nextDayCount
                    nextDayButton.dayWday = nextDayWday
                    nextDayButton.monthNumber = nextMonth
                    nextDayButton:SetScript("OnEnter", function()
                        RaidCalendar_ShowDayTooltip(this.dayNumber, this.dayWday, this.monthNumber)
                    end)
                end
                
                nextDayButton:SetScript("OnLeave", function()
                    GameTooltip:Hide()
                end)
            end

            table.insert(RaidCalendar.dayButtons, nextDayButton)
            
            nextDayCount = nextDayCount + 1
            col = col + 1
        end
    end
end

function RaidCalendar_ClearDayButtons()
    for i, button in ipairs(RaidCalendar.dayButtons) do
        button:Hide()
        button:SetParent(nil)
    end
    RaidCalendar.dayButtons = {}
end

function RaidCalendar_ShowDayTooltip(day, wday, month)
    GameTooltip:SetOwner(this, "ANCHOR_RIGHT")
    GameTooltip:SetText(month .. "月" .. day .. "日")
    
    -- 添加钓鱼大赛信息（每周日）
    if wday == 1 then
        GameTooltip:AddLine("荆棘谷钓鱼大赛", 0, 1, 1)
        GameTooltip:AddLine("时间: 21:00", 1, 1, 1)
        GameTooltip:AddLine(" ")
    end
    
    local currentTime = time()
    local currentYear = date("*t", currentTime).year
    local dayTime = time({
        year = currentYear,
        month = month,
        day = day,
        hour = 12,
        min = 0,
        sec = 0
    })
    
    local isWeeklyReset = (wday == 4)
    local isFiveDayReset = (RaidCalendar_CheckFiveDayReset and RaidCalendar_CheckFiveDayReset(dayTime) and RaidCalendar.fiveDayResetRaids)
    local isThreeDayReset = (RaidCalendar_CheckThreeDayReset and RaidCalendar_CheckThreeDayReset(dayTime) and RaidCalendar.threeDayResetRaids)

    if isWeeklyReset or isFiveDayReset or isThreeDayReset then
        GameTooltip:AddLine("副本重置日", 1, 0.5, 0.5)
        GameTooltip:AddLine("重置时间: 12:00", 1, 1, 1)
        GameTooltip:AddLine(" ")
        GameTooltip:AddLine("重置副本:", 0.5, 0.5, 1)
    end
    
    if isWeeklyReset then
        GameTooltip:AddLine("• 熔火之心", 1, 1, 1)
        GameTooltip:AddLine("• 黑翼之巢", 1, 1, 1)
        GameTooltip:AddLine("• 安其拉神殿", 1, 1, 1)
        GameTooltip:AddLine("• 纳克萨玛斯", 1, 1, 1)
        GameTooltip:AddLine("• 翡翠圣地", 1, 1, 1)
        GameTooltip:AddLine("• 卡拉赞之塔", 1, 1, 1)
    end
    
    if isFiveDayReset then
        for _, raidInfo in ipairs(RaidCalendar.fiveDayResetRaids) do
            GameTooltip:AddLine("• " .. raidInfo.name, 1, 1, 1)
        end
    end
    
    if isThreeDayReset then
        for _, raidInfo in ipairs(RaidCalendar.threeDayResetRaids) do
            GameTooltip:AddLine("• " .. raidInfo.name, 1, 1, 1)
        end
    end
    
    GameTooltip:Show()
end

function RaidCalendar_RefreshUI()
    if not RaidCalendarMainFrame then
        return
    end
    
    RaidCalendar_GenerateCalendar()

end

function RaidCalendar_GetNextResetTime()
    local currentTable = {year = RaidCalendar.currentYear, month = RaidCalendar.currentMonth, day = 1}
    local wday = currentTable.wday
    local daysToReset = 0
    
    if wday < 4 then
        daysToReset = 4 - wday
    elseif wday == 4 then
        if currentTable.hour >= 12 then
            daysToReset = 7
        else
            daysToReset = 0
        end
    else
        daysToReset = 7 - wday + 4
    end
    
    local resetTime = currentTime + (daysToReset * 24 * 3600)
    local resetTable = date("*t", resetTime)
    resetTable.hour = 12
    resetTable.min = 0
    resetTable.sec = 0
    
    return time(resetTable)
end

function RaidCalendar_InitializeFiveDayResets()
    local currentYear = date("*t", time()).year
    local baseDate = {
        year = currentYear,
        month = 6,
        day = 7,
        hour = 12,
        min = 0,
        sec = 0
    }
    RaidCalendar.fiveDayResetBaseTimestamp = time(baseDate)
    RaidCalendar.fiveDayResetRaids = {
        {name = "奥妮克希亚的巢穴", icon = "Interface\\AddOns\\RaidCalendar\\textures\\HL_Icon.tga"},
        {name = "卡拉赞", icon = "Interface\\AddOns\\RaidCalendar\\textures\\KLZ_Icon.tga"},
    }
    RaidCalendar.fiveDaySystemInitialized = true
end

function RaidCalendar_InitializeThreeDayResets()
    local currentYear = date("*t", time()).year
    local baseDate = {
        year = currentYear,
        month = 6,
        day = 3,
        hour = 12,
        min = 0,
        sec = 0
    }
    RaidCalendar.threeDayResetBaseTimestamp = time(baseDate)
    RaidCalendar.threeDayResetRaids = {
        {name = "祖尔格拉布", icon = "Interface\\AddOns\\RaidCalendar\\textures\\ZG_Icon.tga"},
        {name = "安其拉废墟", icon = "Interface\\AddOns\\RaidCalendar\\textures\\AQ20_Icon.tga"},
    }
    RaidCalendar.threeDaySystemInitialized = true
end

RaidCalendar.weeklyRaidIcons = {
    "Interface\\AddOns\\RaidCalendar\\textures\\MC_Icon.tga",
    "Interface\\AddOns\\RaidCalendar\\textures\\BWL_Icon.tga",
    "Interface\\AddOns\\RaidCalendar\\textures\\TAQ_Icon.tga",
    "Interface\\AddOns\\RaidCalendar\\textures\\NAXX_Icon.tga",
    "Interface\\AddOns\\RaidCalendar\\textures\\FC_Icon.tga",
    "Interface\\AddOns\\RaidCalendar\\textures\\K40_Icon.tga"
}

RaidCalendar.battleCycle = {
    { name = "阳光林地山谷", icon = "Interface\\AddOns\\RaidCalendar\\Battle\\SG.tga" },
    { name = "奥特兰克山谷", icon = "Interface\\AddOns\\RaidCalendar\\Battle\\AV.tga" },
    { name = "战歌峡谷"     , icon = "Interface\\AddOns\\RaidCalendar\\Battle\\WSG.tga" },
    { name = "阿拉希盆地"   , icon = "Interface\\AddOns\\RaidCalendar\\Battle\\AB.tga" },
    { name = "血环竞技场"  , icon = "Interface\\AddOns\\RaidCalendar\\Battle\\BR.tga" }
}

-- 新增：白名单检查函数（乌龟服专用，仅角色名）
function RaidCalendar_IsCharacterInWhitelist(characterName)
    if not RaidCalendarWhitelist then
        DEFAULT_CHAT_FRAME:AddMessage("副本日历: 白名单未加载，允许所有同步")
        return true
    end
    
    if not characterName then
        return false
    end
    
    -- 乌龟服只需要检查角色名
    return RaidCalendarWhitelist[characterName] == true
end

-- 新增：获取当前角色名称（乌龟服专用）
function RaidCalendar_GetCurrentCharacterName()
    return UnitName("player")
end

function RaidCalendar_CheckFiveDayReset(timestamp)
    if not RaidCalendar.fiveDayResetBaseTimestamp then
        return false
    end

    local baseDateStrForDebug = date("*t", RaidCalendar.fiveDayResetBaseTimestamp)

    local baseTable = date("*t", RaidCalendar.fiveDayResetBaseTimestamp)
    baseTable.hour = 12
    baseTable.min = 0
    baseTable.sec = 0
    local adjustedBaseTimestamp = time(baseTable)

    local checkTable = date("*t", timestamp)
    checkTable.hour = 12
    checkTable.min = 0
    checkTable.sec = 0
    local adjustedCheckTimestamp = time(checkTable)

    local diffSeconds = adjustedCheckTimestamp - adjustedBaseTimestamp
    local diffDays = diffSeconds / (24 * 3600)
    
    local remainderPositive = 999
    if diffDays >= 0 then
        remainderPositive = math.fmod(diffDays, 5)
    end

    local remainderNegative = 999
    if diffDays < 0 then
        remainderNegative = math.fmod(math.abs(diffDays), 5)
    end

    if diffDays >= 0 and remainderPositive < 0.0001 then
        return true
    elseif diffDays <  0 and remainderNegative < 0.0001 then
        return true
    end

    return false
end

function RaidCalendar_CheckThreeDayReset(timestamp)
    if not RaidCalendar.threeDayResetBaseTimestamp then
        return false
    end

    local baseTable = date("*t", RaidCalendar.threeDayResetBaseTimestamp)
    baseTable.hour = 12
    baseTable.min = 0
    baseTable.sec = 0
    local adjustedBaseTimestamp = time(baseTable)

    local checkTable = date("*t", timestamp)
    checkTable.hour = 12
    checkTable.min = 0
    checkTable.sec = 0
    local adjustedCheckTimestamp = time(checkTable)

    local diffSeconds = adjustedCheckTimestamp - adjustedBaseTimestamp
    local diffDays = diffSeconds / (24 * 3600)
    
    -- 修复：使用正确的模运算逻辑
    local remainder = math.fmod(diffDays, 3)
    if remainder < 0 then
        remainder = remainder + 3
    end
    
    -- 修复：使用合理的精度判断 (小于0.1天 = 2.4小时的容错)
    if remainder < 0.1 or remainder > 2.9 then
        return true
    end
    
    return false
end

function RaidCalendar_GetCurrentBattleIndex()
    local currentYear = date("*t", time()).year
    local baseTimestamp = time{year=currentYear, month=6, day=8, hour=3, min=0, sec=0}
    
    local now = time()
    local today = date("*t", now)
    local todayTimestamp = time{year=today.year, month=today.month, day=today.day, hour=3, min=0, sec=0}
    
    local effectiveTime = todayTimestamp
    if now < todayTimestamp then
        effectiveTime = todayTimestamp - 86400 -- 减去一天
    end
    
    local diffSeconds = effectiveTime - baseTimestamp
    local diffDays = math.floor(diffSeconds / 86400)
    local idx = math.fmod(diffDays, table.getn(RaidCalendar.battleCycle)) + 1
    if idx <= 0 then
        idx = idx + table.getn(RaidCalendar.battleCycle)
    end
    
    return idx
end

function RaidCalendar_GetBattleIndexForDate(year, month, day)
    local currentYear = date("*t", time()).year
    local baseTimestamp = time{year=currentYear, month=6, day=8, hour=3, min=0, sec=0}
    
    local targetTimestamp = time{year=year, month=month, day=day, hour=3, min=0, sec=0}
    
    local diffSeconds = targetTimestamp - baseTimestamp
    local diffDays = math.floor(diffSeconds / 86400)
    local idx = math.fmod(diffDays, table.getn(RaidCalendar.battleCycle)) + 1
    if idx <= 0 then
        idx = idx + table.getn(RaidCalendar.battleCycle)
    end
    
    return idx
end

function RaidCalendar_CreateMinimapButton()
    local button = CreateFrame("Button", "RaidCalendarMinimapButton", Minimap)
    button:SetWidth(33)
    button:SetHeight(33)
    button:SetFrameStrata("MEDIUM")
    button:SetFrameLevel(8)
    
    button:SetPoint("TOPLEFT", Minimap, "TOPLEFT", 52, -2)
    
    local icon = button:CreateTexture("RaidCalendarMinimapIcon", "ARTWORK")
    icon:SetTexture("Interface\\Icons\\INV_Misc_Book_09")
    icon:SetWidth(20)
    icon:SetHeight(20)
    icon:SetPoint("CENTER", button, "CENTER", 0, 1)
    
    local overlay = button:CreateTexture("RaidCalendarMinimapOverlay", "OVERLAY")
    overlay:SetTexture("Interface\\Minimap\\MiniMap-TrackingBorder")
    overlay:SetWidth(53)
    overlay:SetHeight(53)
    overlay:SetPoint("TOPLEFT", button, "TOPLEFT", 0, 0)
    
    -- 注册鼠标点击事件
    button:RegisterForClicks("LeftButtonUp", "RightButtonUp")
    
    -- 修改小地图按钮功能：左键日历，右键副本记录
    button:SetScript("OnClick", function()        
        if arg1 == "LeftButton" then
            -- 左键：打开/关闭日历
            if RaidCalendarMainFrame then
                if RaidCalendarMainFrame:IsVisible() then
                    RaidCalendarMainFrame:Hide()
                    -- 同时隐藏右侧窗口但不重置状态
                    if RaidCalendar.rightFrame then
                        RaidCalendar.rightFrame:Hide()
                    end
                else
                    local currentDate = date("*t")
                    RaidCalendar.currentYear = currentDate.year
                    RaidCalendar.currentMonth = currentDate.month
                    
                    if RaidCalendar_SyncToGuild then
                        RaidCalendar_SyncToGuild()
                    end
                    
                    RaidCalendarMainFrame:Show()
                    
                    -- 重新设置右侧窗口锚点为吸附模式
                    if RaidCalendar.rightFrame then
                        RaidCalendar.rightFrame:ClearAllPoints()
                        RaidCalendar.rightFrame:SetPoint("TOPLEFT", RaidCalendarMainFrame, "TOPRIGHT", 0, 0)
                        
                        -- 恢复右侧窗口状态
                        if RaidCalendar.rightFrameVisible then
                            RaidCalendar.rightFrame:Show()
                            -- 只在首次显示时刷新
                            if type(RaidCalendar_UpdateRaidInfo) == "function" then
                                RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
                            end
                            if type(RaidCalendar_RefreshProfessionData) == "function" then
                                RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
                            end
                        end
                    end
                    
                    RaidCalendar_UpdateMonthYearDisplay()
                    RaidCalendar_RefreshUI()
                end
            end
        elseif arg1 == "RightButton" then
            -- 右键：单独打开/关闭副本记录窗口
            if RaidCalendar.rightFrame then
                if RaidCalendar.rightFrame:IsVisible() then
                    RaidCalendar.rightFrame:Hide()
                    RaidCalendar.rightFrameVisible = false  -- 保存状态
                else
                    -- 如果主界面没打开，使用独立位置
                    if not RaidCalendarMainFrame or not RaidCalendarMainFrame:IsVisible() then
                        RaidCalendar.rightFrame:ClearAllPoints()
                        RaidCalendar.rightFrame:SetPoint("CENTER", UIParent, "CENTER", 200, 0)
                    else
                        -- 如果主界面已打开，使用吸附位置
                        RaidCalendar.rightFrame:ClearAllPoints()
                        RaidCalendar.rightFrame:SetPoint("TOPLEFT", RaidCalendarMainFrame, "TOPRIGHT", 0, 0)
                    end
                    
                    RaidCalendar.rightFrame:Show()
                    RaidCalendar.rightFrameVisible = true   -- 保存状态
                    
                    -- 只在首次显示时刷新
                    if type(RaidCalendar_UpdateRaidInfo) == "function" then
                        RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
                    end
                    if type(RaidCalendar_RefreshProfessionData) == "function" then
                        RaidCalendar_RefreshProfessionData(RaidCalendar.rightPanel)
                    end
                end
            end
        end
    end)

    button:SetScript("OnEnter", function()
        GameTooltip:SetOwner(this, "ANCHOR_LEFT")
        GameTooltip:SetText("副本日历", 1, 1, 1)
        GameTooltip:AddLine("左键: 打开/关闭日历", 0.8, 0.8, 0.8)
        GameTooltip:AddLine("右键: 打开/关闭副本记录", 0.8, 0.8, 0.8)
        GameTooltip:Show()
    end)
    
    button:SetScript("OnLeave", function()
        GameTooltip:Hide()
    end)
    
    RaidCalendar.minimapButton = button
end

function RaidCalendar_OnEvent()
    if event == "ADDON_LOADED" and arg1 == "RaidCalendar" then
        -- 检查 RaidCalendarDB 是否从文件加载
        if RaidCalendarDB then
            -- 显示当前数据结构概况
            local charCount = 0
            local guildCount = 0
            if RaidCalendarDB.characters then
                for _ in pairs(RaidCalendarDB.characters) do
                    charCount = charCount + 1
                end
            end
            if RaidCalendarDB.guildRaids then
                for _ in pairs(RaidCalendarDB.guildRaids) do
                    guildCount = guildCount + 1
                end
            end
        else
            RaidCalendarDB = {
                characters = {},
                guildRaids = {}
            }
        end
        -- 初始化重置系统
        if not RaidCalendar.fiveDaySystemInitialized then
            if type(RaidCalendar_InitializeFiveDayResets) == "function" then
                RaidCalendar_InitializeFiveDayResets()
                RaidCalendar.fiveDaySystemInitialized = true
            end
        end
        if not RaidCalendar.threeDaySystemInitialized then
            if type(RaidCalendar_InitializeThreeDayResets) == "function" then
                RaidCalendar_InitializeThreeDayResets()
                RaidCalendar.threeDaySystemInitialized = true
            end
        end

        -- 确保数据库结构正确初始化
        if not RaidCalendarDB.characters then
            RaidCalendarDB.characters = {}
        end
        
        if not RaidCalendarDB.guildRaids then
            RaidCalendarDB.guildRaids = {}
        end
        
        -- 安全清理已保存的数据
        if RaidCalendarDB then
            -- 清理可能有问题的明文时间戳数据
            pcall(RaidCalendar_CleanupCorruptedData)
        else
            RaidCalendarDB = {
                characters = {},
                guildRaids = {}
            }
        end
        
        DEFAULT_CHAT_FRAME:AddMessage("副本日历: 插件已加载，输入 /rc 打开界面")
        
        RaidCalendar_CreateMinimapButton()

        -- 延迟初始化右侧面板，确保所有函数都已加载
        local function delayedInit()
            -- 检查并初始化右侧面板
            if RaidCalendar.rightPanel then
                if type(RaidCalendar_CreateRightPanel) == "function" then
                    RaidCalendar_CreateRightPanel(RaidCalendar.rightPanel)
                end
                
                -- 创建专业技能面板
                if type(RaidCalendar_CreateProfessionPanel) == "function" then
                    RaidCalendar_CreateProfessionPanel(RaidCalendar.rightPanel)
                end
            end
            
            -- 刷新UI
            RaidCalendar_RefreshUI()
        end
        
        -- 使用计时器延迟执行，确保所有模块都已加载
        local delayFrame = CreateFrame("Frame")
        local elapsed = 0
        delayFrame:SetScript("OnUpdate", function()
            elapsed = elapsed + arg1
            if elapsed >= 0.5 then -- 延迟0.5秒
                delayFrame:SetScript("OnUpdate", nil)
                delayedInit()
            end
        end)

    elseif event == "PLAYER_LOGIN" then
        -- 只保留基本登录处理，移除小队/团队同步功能
        
    elseif event == "CHAT_MSG_ADDON" and arg1 == RaidCalendar.MSG_PREFIX then
        -- 保留：副本记录模块有自己的消息处理

    -- 移除：不再处理小队/团队变化事件
    -- elseif event == "PARTY_MEMBERS_CHANGED" then
    -- elseif event == "RAID_ROSTER_UPDATE" then
    end
end

-- 新增：清理可能损坏的数据
function RaidCalendar_CleanupCorruptedData()
    if not RaidCalendarDB then
        return
    end
    
    local cleaned = false
    
    -- 清理characters数据中的异常时间戳
    if RaidCalendarDB.characters then
        for charName, charData in pairs(RaidCalendarDB.characters) do
            if charData then
                -- 检查lastUpdate是否为有效数字（8位数字范围：01010000 到 12312359）
                if charData.lastUpdate and (type(charData.lastUpdate) ~= "number" or charData.lastUpdate > 99999999 or charData.lastUpdate < 1000000) then
                    charData.lastUpdate = time()
                    cleaned = true
                end
                
                -- 清理raids数据中的异常时间戳
                if charData.raids then
                    for i = table.getn(charData.raids), 1, -1 do
                        local raid = charData.raids[i]
                        if raid and raid.readableTime then
                            -- 如果明文时间戳不是8位数字，删除这个条目
                            if type(raid.readableTime) ~= "number" or raid.readableTime > 99999999 or raid.readableTime < 1000000 then
                                table.remove(charData.raids, i)
                                cleaned = true
                            end
                        end
                    end
                end
            end
        end
    end
    
    -- 清理guildRaids数据中的异常时间戳  
    if RaidCalendarDB.guildRaids then
        for playerName, playerRaids in pairs(RaidCalendarDB.guildRaids) do
            if type(playerRaids) == "table" then
                for raidName, raidEntries in pairs(playerRaids) do
                    if type(raidEntries) == "table" then
                        for i = table.getn(raidEntries), 1, -1 do
                            local entry = raidEntries[i]
                            if entry and entry.readableTime then
                                -- 如果明文时间戳不是8位数字，删除这个条目
                                if type(entry.readableTime) ~= "number" or entry.readableTime > 99999999 or entry.readableTime < 1000000 then
                                    table.remove(raidEntries, i)
                                    cleaned = true
                                end
                            end
                        end
                        
                        -- 如果数组为空，删除整个条目
                        if table.getn(raidEntries) == 0 then
                            playerRaids[raidName] = nil
                        end
                    end
                end
                
                -- 如果玩家没有任何有效数据，删除整个玩家条目
                if not next(playerRaids) then
                    RaidCalendarDB.guildRaids[playerName] = nil
                end
            end
        end
    end
    
    if cleaned then
        DEFAULT_CHAT_FRAME:AddMessage("副本日历: 已清理损坏的数据")
    end
end

SLASH_RAIDCALENDAR1 = "/rc"
SlashCmdList["RAIDCALENDAR"] = RaidCalendar_SlashHandler

local frame = CreateFrame("Frame")
frame:RegisterEvent("ADDON_LOADED")
frame:RegisterEvent("PLAYER_LOGIN")
frame:RegisterEvent("CHAT_MSG_ADDON")  -- 保留：工会同步需要
-- 移除：不再注册小队/团队事件
-- frame:RegisterEvent("PARTY_MEMBERS_CHANGED")
-- frame:RegisterEvent("RAID_ROSTER_UPDATE")
frame:SetScript("OnEvent", RaidCalendar_OnEvent)

-- 新增：智能调整右侧窗口大小
function RaidCalendar_AdjustRightFrameSize()
    if not RaidCalendar.rightFrame then return end
    
    -- 检查专业技能模块是否有可见内容
    local professionPanelVisible = false
    
    for _, region in ipairs({ RaidCalendar.rightFrame:GetRegions() }) do
        if region and region.GetText and region:GetText() == "专业技能冷却" and region:IsVisible() then
            professionPanelVisible = true
            break
        end
    end
    
    -- 只调整窗口宽度，让各模块自行处理内部布局
    if professionPanelVisible then
        RaidCalendar.rightFrame:SetWidth(400)
    else
        RaidCalendar.rightFrame:SetWidth(210)
    end

end