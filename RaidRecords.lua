--[[
    副本记录模块 (RaidRecords)
    ========================================
    
    功能说明：
    - 管理角色的副本进度记录
    - 自动同步工会成员副本状态
    - 智能数据清理和过期处理
    - 支持多种副本重置周期
    
    主要组件：
    1. 副本数据管理
       - 自动保存当前角色副本进度
       - 工会成员副本信息同步
       - 智能数据变化检测
    
    2. 界面显示
       - 右侧面板副本记录展示
       - 角色职业颜色显示
       - 副本ID智能分组和着色
       - 可展开/收缩的副本详情
    
    3. 数据清理系统
       - 自动清理过期副本数据
       - 定时清理无时间戳的旧数据
       - 支持手动清理所有数据
    
    4. 通信机制
       - 工会频道数据交换
       - 小队/团队实时同步
       - 防重复发送冷却机制
    
    数据结构：
    - RaidCalendarDB.characters[角色名].raids - 个人副本数据
    - RaidCalendarDB.guildRaids[角色名] - 工会同步数据
    
    重置周期配置：
    - 周重置：7天 (MC、BWL、TAQ等)
    - 5天重置：奥妮、卡拉赞
    - 3天重置：ZG、AQ20
    
    作者：拉风乌龟
--]]

-- 新增：本地副本重置时间计算函数（备用）
local function RaidRecords_GetWeeklyResetTime()
    local currentTime = time()
    local currentTable = date("*t", currentTime)
    local currentWday = currentTable.wday
    local daysToWednesday = 0
    
    if currentWday < 4 then  -- 今天是周日到周二
        daysToWednesday = 4 - currentWday
    elseif currentWday == 4 then  -- 今天是周三
        if currentTable.hour >= 12 then
            daysToWednesday = 7  -- 已过12点，下周三
        else
            daysToWednesday = 0  -- 今天12点
        end
    else  -- 今天是周四到周六
        daysToWednesday = 7 - currentWday + 4
    end
    
    local nextWednesday = currentTime + (daysToWednesday * 24 * 3600)
    local wednesdayTable = date("*t", nextWednesday)
    wednesdayTable.hour = 12
    wednesdayTable.min = 0
    wednesdayTable.sec = 0
    
    return time(wednesdayTable)
end

RaidCalendar.RaidRecords = {}

-- 删除：硬编码的副本重置时间配置
-- RaidCalendar.RaidResetConfig = {
--     -- 周重置副本（每周三12:00重置）
--     weekly = {
--         ["熔火之心"] = 7 * 24 * 3600,
--         ["黑翼之巢"] = 7 * 24 * 3600,
--         ["安其拉神殿"] = 7 * 24 * 3600,
--         ["纳克萨玛斯"] = 7 * 24 * 3600,
--         ["翡翠圣地"] = 7 * 24 * 3600,
--         ["卡拉赞之塔"] = 7 * 24 * 3600,
--     },
--     -- 5天重置副本
--     fiveDay = {
--         ["奥妮克希亚的巢穴"] = 5 * 24 * 3600,
--         ["卡拉赞"] = 5 * 24 * 3600,
--     },
--     -- 3天重置副本
--     threeDay = {
--         ["祖尔格拉布"] = 3 * 24 * 3600,
--         ["安其拉废墟"] = 3 * 24 * 3600,
--     }
-- }

-- 修改：使用主文件的真实副本更新时间计算
function RaidCalendar_GetRaidResetInterval(raidName)
    if not raidName then
        return 7 * 24 * 3600 -- 默认7天
    end
    
    -- 调用主文件的函数获取真实的副本更新时间
    if type(RaidCalendar_GetRaidUpdateTime) == "function" then
        local updateTime = RaidCalendar_GetRaidUpdateTime(raidName)
        if updateTime and updateTime > 0 then
            -- 返回到下次更新的时间间隔
            local currentTime = time()
            return updateTime - currentTime
        end
    end
    
    -- 如果主文件函数不可用或返回无效值，使用默认值
    return 7 * 24 * 3600
end

-- 修改：8位明文时间戳函数（移除年份）
function RaidCalendar_GetReadableTimestamp()
    local timeTable = date("*t", time())
    local month = timeTable.month
    local day = timeTable.day
    local hour = timeTable.hour
    local min = timeTable.min
    
    -- 确保各部分都在合理范围内
    if month < 1 or month > 12 then month = 1 end
    if day < 1 or day > 31 then day = 1 end
    if hour < 0 or hour > 23 then hour = 0 end
    if min < 0 or min > 59 then min = 0 end
    
    -- 8位格式：MMDDHHMM (月日时分)
    return month * 1000000 + day * 10000 + hour * 100 + min
end

-- 修改：8位明文时间戳转Unix时间戳
function RaidCalendar_ReadableToUnixTimestamp(readableTimestamp)
    -- 检查输入是否有效（8位数字范围）
    if not readableTimestamp or type(readableTimestamp) ~= "number" or readableTimestamp <= 0 then
        return time() -- 返回当前时间
    end
    
    -- 防止数值过大或过小
    if readableTimestamp > 99999999 or readableTimestamp < 1000000 then
        return time() -- 返回当前时间
    end
    
    -- 解析8位时间戳：MMDDHHMM
    local month = math.floor(readableTimestamp / 1000000)
    local remainder = math.mod(readableTimestamp, 1000000)
    local day = math.floor(remainder / 10000)
    remainder = math.mod(remainder, 10000)
    local hour = math.floor(remainder / 100)
    local min = math.mod(remainder, 100)
    
    -- 验证各部分是否在合理范围内
    if month < 1 or month > 12 or day < 1 or day > 31 or 
       hour < 0 or hour > 23 or min < 0 or min > 59 then
        return time() -- 返回当前时间
    end
    
    -- 使用当前年份创建时间戳
    local currentYear = date("*t", time()).year
    
    -- 安全创建时间戳
    local success, result = pcall(time, {
        year = currentYear,
        month = month,
        day = day,
        hour = hour,
        min = min,
        sec = 0
    })
    
    if success and result then
        return result
    else
        return time() -- 如果创建失败，返回当前时间
    end
end

-- 修改：检查副本数据是否过期（只支持明文时间戳）
function RaidCalendar_IsRaidDataExpired(raidInfo)
    if not raidInfo or not raidInfo.name or not raidInfo.readableTime then
        return true -- 如果数据不完整或没有明文时间戳，认为已过期
    end

    local currentTime = time()
    local raidTimestamp = RaidCalendar_ReadableToUnixTimestamp(raidInfo.readableTime)

    -- 7天重置副本（周三12点重置）
    if string.find(raidInfo.name, "熔火之心") or string.find(raidInfo.name, "黑翼之巢") or 
       string.find(raidInfo.name, "安其拉神殿") or string.find(raidInfo.name, "纳克萨玛斯") or
       string.find(raidInfo.name, "翡翠圣地") or string.find(raidInfo.name, "卡拉赞之塔") then
        -- 计算本周三12点的时间戳
        local now = date("*t", currentTime)
        local wday = now.wday -- 1=周日, 2=周一, ..., 4=周三, ..., 7=周六
        -- 用math.mod替代%
        local daysSinceWednesday = math.mod(wday - 4, 7)
        local thisWednesday = time({
            year = now.year,
            month = now.month,
            day = now.day - daysSinceWednesday,
            hour = 12,
            min = 0,
            sec = 0
        })
        -- 如果当前时间已过本周三12点，且副本数据早于本周三12点，则过期
        if currentTime >= thisWednesday and raidTimestamp < thisWednesday then
            return true
        end
        return false
    end

    -- 3天重置副本
    if string.find(raidInfo.name, "祖尔格拉布") or string.find(raidInfo.name, "安其拉废墟") then
        if type(RaidCalendar_CheckThreeDayReset) == "function" and RaidCalendar.threeDayResetBaseTimestamp then
            for checkDay = 1, 7 do
                local checkTime = raidTimestamp + (checkDay * 24 * 3600)
                if RaidCalendar_CheckThreeDayReset(checkTime) then
                    local checkTable = date("*t", checkTime)
                    local resetTime = time({
                        year = checkTable.year,
                        month = checkTable.month,
                        day = checkTable.day,
                        hour = 12,
                        min = 0,
                        sec = 0
                    })
                    if currentTime >= resetTime and raidTimestamp < resetTime then
                        return true
                    end
                    break
                end
            end
        end
        return false
    end

    -- 5天重置副本
    if string.find(raidInfo.name, "奥妮克希亚") or string.find(raidInfo.name, "卡拉赞") then
        if type(RaidCalendar_CheckFiveDayReset) == "function" and RaidCalendar.fiveDayResetBaseTimestamp then
            for checkDay = 1, 8 do
                local checkTime = raidTimestamp + (checkDay * 24 * 3600)
                if RaidCalendar_CheckFiveDayReset(checkTime) then
                    local checkTable = date("*t", checkTime)
                    local resetTime = time({
                        year = checkTable.year,
                        month = checkTable.month,
                        day = checkTable.day,
                        hour = 12,
                        min = 0,
                        sec = 0
                    })
                    if currentTime >= resetTime and raidTimestamp < resetTime then
                        return true
                    end
                    break
                end
            end
        end
        return false
    end

    -- 默认：7天过期
    return (currentTime - raidTimestamp) > (7 * 24 * 3600)
end

-- 删除：重复的函数定义，只保留一个正确的版本
-- 修改：详细的清理函数，添加副本更新时间输出
function RaidCalendar_AutoCleanupExpiredRaidData()
    if not RaidCalendarDB then
        return 0
    end
    
    local cleanupCount = 0
    
    -- 清理角色数据中的过期副本
    if RaidCalendarDB.characters then
        for characterName, characterData in pairs(RaidCalendarDB.characters) do
            if characterData and characterData.raids then
                local cleanRaids = {}
                
                for _, raidInfo in ipairs(characterData.raids) do
                    if not RaidCalendar_IsRaidDataExpired(raidInfo) then
                        table.insert(cleanRaids, raidInfo)
                    else
                        cleanupCount = cleanupCount + 1
                    end
                end
                
                characterData.raids = cleanRaids
            end
        end
    end
    
    -- 清理工会数据中的过期副本
    if RaidCalendarDB.guildRaids then
        for playerName, playerRaids in pairs(RaidCalendarDB.guildRaids) do
            if type(playerRaids) == "table" then
                for raidName, raidEntries in pairs(playerRaids) do
                    if type(raidEntries) == "table" then
                        local cleanEntries = {}
                        
                        for _, raidInfo in ipairs(raidEntries) do
                            if raidInfo.readableTime then
                                local completeRaidInfo = {
                                    name = raidName,
                                    id = raidInfo.id,
                                    readableTime = raidInfo.readableTime, 
                                    class = raidInfo.class
                                }
                                
                                if not RaidCalendar_IsRaidDataExpired(completeRaidInfo) then
                                    table.insert(cleanEntries, raidInfo)
                                else
                                    cleanupCount = cleanupCount + 1
                                end
                            else
                                cleanupCount = cleanupCount + 1
                            end
                        end
                        
                        if next(cleanEntries) ~= nil then
                            playerRaids[raidName] = cleanEntries
                        else
                            playerRaids[raidName] = nil
                        end
                    end
                end
                
                if next(playerRaids) == nil then
                    RaidCalendarDB.guildRaids[playerName] = nil
                end
            end
        end
    end
    
    if cleanupCount > 0 then
        DEFAULT_CHAT_FRAME:AddMessage(string.format("副本日历: 清理了 %d 条过期数据", cleanupCount))
    end
    return cleanupCount
end

-- 修改：简化登录处理
function RaidCalendar_RaidRecordsOnLogin()
    DEFAULT_CHAT_FRAME:AddMessage("=== 副本记录模块登录处理开始 ===")
    
    -- 安全获取玩家名称
    local playerName = nil
    pcall(function()
        if GetUnitName then
            playerName = GetUnitName("player")
        elseif UnitName then
            playerName = UnitName("player")
        end
    end)
    
    if not playerName or playerName == "" then
        DEFAULT_CHAT_FRAME:AddMessage("登录调试: 无法获取玩家名称，退出")
        return
    end
    
    DEFAULT_CHAT_FRAME:AddMessage("登录调试: 玩家名称: " .. playerName)

    -- 静默清理，添加安全保护
    DEFAULT_CHAT_FRAME:AddMessage("登录调试: 开始清理数据")
    pcall(RaidCalendar_CleanupLegacyData)
    pcall(RaidCalendar_AutoCleanupExpiredRaidData)
    DEFAULT_CHAT_FRAME:AddMessage("登录调试: 数据清理完成")
    
    -- 检查白名单
    local currentCharacter = playerName
    if type(RaidCalendar_GetCurrentCharacterName) == "function" then
        currentCharacter = RaidCalendar_GetCurrentCharacterName()
        DEFAULT_CHAT_FRAME:AddMessage("登录调试: 使用白名单函数获取角色名")
    end
    
    if type(RaidCalendar_IsCharacterInWhitelist) == "function" and not RaidCalendar_IsCharacterInWhitelist(currentCharacter) then
        DEFAULT_CHAT_FRAME:AddMessage("登录调试: 角色不在白名单中，退出")
        return
    end
    
    DEFAULT_CHAT_FRAME:AddMessage("登录调试: 白名单检查通过")
    
    -- 延迟登录同步
    if IsInGuild and IsInGuild() then
        DEFAULT_CHAT_FRAME:AddMessage("登录调试: 在工会中，准备登录同步")
        local loginDelay = math.random(180, 600) -- 3-10分钟随机延迟
        DEFAULT_CHAT_FRAME:AddMessage("登录调试: 设置登录延迟: " .. loginDelay .. " 秒")
        
        local loginSyncFrame = CreateFrame("Frame")
        local loginElapsed = 0
        loginSyncFrame:SetScript("OnUpdate", function()
            loginElapsed = loginElapsed + arg1
            if loginElapsed >= loginDelay then
                loginSyncFrame:SetScript("OnUpdate", nil)
                
                DEFAULT_CHAT_FRAME:AddMessage("=== 登录延迟时间到，开始同步处理 ===")
                
                if type(RaidCalendar_SaveCurrentCharacterRaids) == "function" then
                    DEFAULT_CHAT_FRAME:AddMessage("登录同步: 保存当前角色副本数据")
                    RaidCalendar_SaveCurrentCharacterRaids()
                else
                    DEFAULT_CHAT_FRAME:AddMessage("登录同步: 保存副本数据函数不可用")
                end
                
                -- 准备队列消息
                if playerName and RaidCalendarDB and RaidCalendarDB.characters and RaidCalendarDB.characters[playerName] then
                    local playerRaids = RaidCalendarDB.characters[playerName].raids or {}
                    local raidCount = 0
                    for _ in pairs(playerRaids) do raidCount = raidCount + 1 end
                    DEFAULT_CHAT_FRAME:AddMessage("登录同步: 当前角色副本数量: " .. raidCount)
                    
                    local _, playerClassToken = UnitClass("player")
                    if not playerClassToken then playerClassToken = "UNKNOWN" end
                    
                    local message = ""
                    if next(playerRaids) ~= nil then
                        local messageParts = {}
                        for _, raidInfo in pairs(playerRaids) do
                            local name = raidInfo.name or "UnknownRaid"
                            local id = raidInfo.id or 0
                            local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
                            table.insert(messageParts, name .. "," .. id .. "," .. playerClassToken .. "," .. readableTime)
                        end
                        message = table.concat(messageParts, ";")
                        DEFAULT_CHAT_FRAME:AddMessage("登录同步: 生成消息长度: " .. string.len(message))
                    else
                        message = "NO_RAID_LOCKS"
                        DEFAULT_CHAT_FRAME:AddMessage("登录同步: 无副本数据")
                    end
                    
                    -- 加入队列
                    if not RaidCalendar.globalSyncLimiter.loginSyncQueue then
                        RaidCalendar.globalSyncLimiter.loginSyncQueue = {}
                    end
                    
                    table.insert(RaidCalendar.globalSyncLimiter.loginSyncQueue, {
                        playerName = playerName,
                        message = message,
                        timestamp = time()
                    })
                    
                    DEFAULT_CHAT_FRAME:AddMessage("登录同步: 已加入队列")
                    RaidCalendar_ProcessLoginSyncQueue()
                end
                
                if type(RaidCalendar_GetDataHash) == "function" then
                    RaidCalendar.lastDataHash = RaidCalendar_GetDataHash()
                    DEFAULT_CHAT_FRAME:AddMessage("登录同步: 数据哈希初始化完成")
                end
                
                DEFAULT_CHAT_FRAME:AddMessage("=== 登录同步处理完成 ===")
            end
        end)
    else
        DEFAULT_CHAT_FRAME:AddMessage("登录调试: 不在工会中，跳过同步")
    end
    
    DEFAULT_CHAT_FRAME:AddMessage("=== 副本记录模块登录处理设置完成 ===")
end

-- 修改：队列处理，添加队列大小限制
function RaidCalendar_ProcessLoginSyncQueue()
    DEFAULT_CHAT_FRAME:AddMessage("=== 开始处理登录同步队列 ===")
    
    if RaidCalendar.globalSyncLimiter.isProcessingQueue then
        DEFAULT_CHAT_FRAME:AddMessage("队列处理: 队列正在处理中，退出")
        return
    end
    
    local queue = RaidCalendar.globalSyncLimiter.loginSyncQueue
    if not queue or table.getn(queue) == 0 then
        DEFAULT_CHAT_FRAME:AddMessage("队列处理: 队列为空，退出")
        return
    end
    
    -- 限制队列大小，防止内存泄露
    local maxQueueSize = 5
    if table.getn(queue) > maxQueueSize then
        DEFAULT_CHAT_FRAME:AddMessage("队列处理: 队列过大，只保留最新的 " .. maxQueueSize .. " 个项目")
        local newQueue = {}
        local startIndex = table.getn(queue) - maxQueueSize + 1
        for i = startIndex, table.getn(queue) do
            table.insert(newQueue, queue[i])
        end
        RaidCalendar.globalSyncLimiter.loginSyncQueue = newQueue
        queue = newQueue
    end
    
    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 开始处理 " .. table.getn(queue) .. " 个项目")
    RaidCalendar.globalSyncLimiter.isProcessingQueue = true
    
    -- 每次只处理一个，间隔30秒
    local processFrame = CreateFrame("Frame")
    local processInterval = 30 -- 30秒间隔
    local currentIndex = 1
    
    local function processNext()
        if currentIndex <= table.getn(queue) then
            local syncData = queue[currentIndex]
            DEFAULT_CHAT_FRAME:AddMessage("队列处理: 处理第 " .. currentIndex .. " 个项目")
            
            if syncData and syncData.playerName and syncData.message then
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 发送消息 - 角色: " .. syncData.playerName)
                
                -- 发送消息
                if SendAddonMessage and RaidCalendar.MSG_PREFIX then
                    SendAddonMessage(RaidCalendar.MSG_PREFIX, syncData.message, "GUILD")
                    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 消息已发送到工会频道")
                else
                    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 发送消息失败")
                end
            else
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 无效的同步数据，跳过")
            end
            
            currentIndex = currentIndex + 1
            
            -- 继续下一个
            if currentIndex <= table.getn(queue) then
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 等待 " .. processInterval .. " 秒后处理下一个")
                local nextFrame = CreateFrame("Frame")
                local nextElapsed = 0
                nextFrame:SetScript("OnUpdate", function()
                    nextElapsed = nextElapsed + arg1
                    if nextElapsed >= processInterval then
                        nextFrame:SetScript("OnUpdate", nil)
                        processNext()
                    end
                end)
            else
                -- 队列处理完成
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 所有项目处理完成")
                RaidCalendar.globalSyncLimiter.loginSyncQueue = {}
                RaidCalendar.globalSyncLimiter.isProcessingQueue = false
                DEFAULT_CHAT_FRAME:AddMessage("=== 登录同步队列处理完成 ===")
            end
        end
    end
    
    -- 开始处理第一个
    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 立即开始处理第一个项目")
    processNext()
end

-- 删除：移除定期清理系统
-- RaidCalendar.autoCleanupConfig = { ... }
-- function RaidCalendar_StartAutoCleanupSystem() ... end

function RaidCalendar_CreateRightPanel(parentFrame)
    local playerNameDisplay = GetUnitName("player") or "未知角色"
    local localizedPlayerClass, playerClassToken = UnitClass("player")
    local hexColor = RaidCalendar_GetClassColorHex(playerClassToken)
    
    -- 添加副本记录标题
    local raidTitle = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    raidTitle:SetPoint("TOPLEFT", parentFrame, "TOPLEFT", 75, -20)
    raidTitle:SetText("副本记录")
    raidTitle:SetTextColor(1, 1, 0)
    raidTitle.elementType = "raidRecord"
    
    -- 将标题保存到父窗口以便后续引用
    parentFrame.raidTitle = raidTitle

    local charInfo = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    charInfo:SetPoint("TOP", raidTitle, "BOTTOM", 0, -15)  -- 锚定副本记录标题，居中对齐，向下15像素
    charInfo:SetText("角色: |cFF" .. hexColor .. playerNameDisplay .. "|r (" .. localizedPlayerClass .. ")")
    charInfo.elementType = "raidRecord"

    local serverName = GetRealmName() or "未知服务器"
    local serverInfo = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    serverInfo:SetPoint("TOP", charInfo, "BOTTOM", 0, -5)  -- 相对于角色信息居中
    serverInfo:SetText("服务器: " .. serverName)
    serverInfo:SetTextColor(0.8, 0.8, 1)
    serverInfo.elementType = "raidRecord"

    parentFrame.raidElements = {}
    
    RaidCalendar_UpdateRaidInfo(parentFrame)
end

function RaidCalendar_UpdateRaidInfo(parentFrame)
    if not parentFrame then
        return
    end
    
    if parentFrame.raidElements then
        for _, element in ipairs(parentFrame.raidElements) do
            if element and element.Hide then
                element:Hide()
            end
        end
    end
    parentFrame.raidElements = {}
    
    local yOffset = -110
    
    local hasRaids = false
    
    local raidGroups = {}
    
    if not parentFrame.raidGroupStates then
        parentFrame.raidGroupStates = {}
    end
    
    -- 获取副本记录标题的引用
    local raidTitle = nil
    for _, child in ipairs({parentFrame:GetChildren()}) do
        if child.elementType == "raidRecord" and child:GetText() == "副本记录" then
            raidTitle = child
            break
        end
    end
    
    if GetNumSavedInstances then
        local numSavedInstances = GetNumSavedInstances()
        if numSavedInstances and numSavedInstances > 0 then
            for i = 1, numSavedInstances do
                local instanceName, instanceID, instanceReset = GetSavedInstanceInfo(i)
                if instanceName then
                    if not raidGroups[instanceName] then
                        raidGroups[instanceName] = { players = {}, expanded = false }
                    end
                    local _, playerClassToken = UnitClass("player")
                    table.insert(raidGroups[instanceName].players, {
                        name = GetUnitName("player") or "未知角色",
                        id = instanceID or "未知",
                        isCurrent = true,
                        class = playerClassToken
                    })
                end
            end
        end
    end
    
    if RaidCalendarDB and RaidCalendarDB.guildRaids then
        for playerName, playerGuildData in pairs(RaidCalendarDB.guildRaids) do
            if playerName ~= GetUnitName("player") then
                if type(playerGuildData) == "table" then
                    for raidName, raidEntries in pairs(playerGuildData) do
                        if type(raidEntries) == "table" then
                            for _, raidInfo in ipairs(raidEntries) do
                                if raidName and raidInfo.id then
                                    if not raidGroups[raidName] then
                                        raidGroups[raidName] = { players = {}, expanded = false }
                                    end
                                    table.insert(raidGroups[raidName].players, {
                                        name = playerName,
                                        id = raidInfo.id,
                                        isCurrent = false,
                                        class = raidInfo.class
                                    })
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    
    for raidName, raidGroup in pairs(raidGroups) do
        hasRaids = true
        
        if parentFrame.raidGroupStates[raidName] ~= nil then
            raidGroup.expanded = parentFrame.raidGroupStates[raidName]
        else
            raidGroup.expanded = false
            parentFrame.raidGroupStates[raidName] = false
        end
        
        if raidGroup.players then
            table.sort(raidGroup.players, function(a, b)
                -- 优先级1: 当前角色排在最前面
                if a.isCurrent and not b.isCurrent then
                    return true -- a是当前角色，b不是，a排前面
                elseif not a.isCurrent and b.isCurrent then
                    return false -- b是当前角色，a不是，b排前面
                end
                
                -- 优先级2: 如果都是当前角色或都不是当前角色，按副本ID排序
                -- 但要考虑当前角色的ID，让相同ID的角色聚集在一起
                local currentPlayerID = nil
                for _, playerInfo in ipairs(raidGroup.players) do
                    if playerInfo.isCurrent then
                        currentPlayerID = playerInfo.id
                        break
                    end
                end
                
                if currentPlayerID then
                    -- 如果有当前角色，优先显示与当前角色相同ID的其他角色
                    local aIsCurrentID = (tostring(a.id) == tostring(currentPlayerID))
                    local bIsCurrentID = (tostring(b.id) == tostring(currentPlayerID))
                    
                    if aIsCurrentID and not bIsCurrentID then
                        return true -- a与当前角色同ID，b不是，a排前面
                    elseif not aIsCurrentID and bIsCurrentID then
                        return false -- b与当前角色同ID，a不是，b排前面
                    end
                end
                
                -- 优先级3: 如果ID关系相同，按副本ID数字排序
                if a.id ~= b.id then
                    return tostring(a.id) < tostring(b.id)
                end
                
                -- 优先级4: 如果副本ID相同，按角色名排序
                return a.name < b.name
            end)
        end
        
        local idCounts = {}
        if raidGroup.players then
            for _, playerInfo in ipairs(raidGroup.players) do
                if playerInfo.id then
                    idCounts[playerInfo.id] = (idCounts[playerInfo.id] or 0) + 1
                end
            end
        end

        local instanceIdToColorMap = {}
        local nextSharedColorIndex = 1
        
        local expandButton = CreateFrame("Button", "RaidGroupExpand_" .. raidName, parentFrame)
        expandButton:SetPoint("TOPLEFT", parentFrame, "TOPLEFT", 15, yOffset)  -- 锚定左上角，距离左边界15像素
        expandButton:SetWidth(180)
        expandButton:SetHeight(20)
        
        local hasCurrentPlayer = false
        for _, playerInfo in ipairs(raidGroup.players) do
            if playerInfo.isCurrent then
                hasCurrentPlayer = true
                break
            end
        end
        
        local buttonBg = expandButton:CreateTexture(nil, "BACKGROUND")
        buttonBg:SetAllPoints(expandButton)
        if hasCurrentPlayer then
            buttonBg:SetTexture(0.8, 0.3, 0.3, 0.5)
        else
            buttonBg:SetTexture(0.2, 0.2, 0.2, 0.5)
        end

        expandButton:SetBackdrop({
          edgeFile = "Interface\\Buttons\\WHITE8X8",
          edgeSize = 1,
        })
        expandButton:SetBackdropBorderColor(0.5, 0.5, 0.5, 0.3)
        expandButton:SetBackdropColor(0, 0, 0, 0)
        
        local raidNameText = expandButton:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
        raidNameText:SetPoint("LEFT", expandButton, "LEFT", 50, 0)
        local playerCount = table.getn(raidGroup.players)
        raidNameText:SetText(raidName .. " (" .. playerCount .. "人)")
        
        local expandIcon = expandButton:CreateTexture(nil, "OVERLAY")
        expandIcon:SetPoint("LEFT", expandButton, "LEFT", 5, 0)
        expandIcon:SetWidth(16)
        expandIcon:SetHeight(16)
        
        if raidGroup.expanded then
            expandIcon:SetTexture("Interface\\Buttons\\UI-MinusButton-Up")
        else
            expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
        end
        
        local raidIcon = expandButton:CreateTexture(nil, "OVERLAY")
        raidIcon:SetPoint("LEFT", expandButton, "LEFT", 25, 0)
        raidIcon:SetWidth(20)
        raidIcon:SetHeight(20)
        
        local iconPath = RaidCalendar_GetRaidIcon(raidName)
        raidIcon:SetTexture(iconPath)
        
        expandButton.expandIcon = expandIcon
        expandButton.raidIcon = raidIcon

        expandButton.expanded = raidGroup.expanded
        expandButton.raidName = raidName
        expandButton.players = raidGroup.players
        expandButton.nameText = raidNameText
        expandButton.parentFrame = parentFrame
        
        local detailFrame = CreateFrame("Frame", "RaidGroupDetail_" .. raidName, parentFrame)
        detailFrame:SetPoint("TOPLEFT", expandButton, "BOTTOMLEFT", 0, -7)  -- 确保锚定到按钮下方
        detailFrame:SetWidth(180)  -- 调整宽度，因为有了15像素偏移
        
        local maxVisiblePlayers = 10
        local playerDisplayHeight = math.min(playerCount, maxVisiblePlayers) * 18
        detailFrame:SetHeight(playerDisplayHeight)
        
        expandButton.detailFrame = detailFrame
        
        -- 确保详情框初始隐藏
        detailFrame:Hide()
        
        local scrollFrame = nil
        local scrollChild = nil
        local scrollBar = nil
        
        if playerCount > maxVisiblePlayers then
            scrollFrame = CreateFrame("ScrollFrame", "RaidDetailScroll_" .. raidName, detailFrame)
            scrollFrame:SetPoint("TOPLEFT", detailFrame, "TOPLEFT", 0, 0)
            scrollFrame:SetPoint("BOTTOMRIGHT", detailFrame, "BOTTOMRIGHT", -16, 0)
            
            scrollChild = CreateFrame("Frame", "RaidDetailScrollChild_" .. raidName, scrollFrame)
            scrollChild:SetWidth(184)  -- 调整宽度适应滚动条
            scrollChild:SetHeight(playerCount * 18)
            scrollFrame:SetScrollChild(scrollChild)
            
            scrollBar = CreateFrame("Slider", "RaidDetailScrollBar_" .. raidName, detailFrame)
            scrollBar:SetPoint("TOPRIGHT", detailFrame, "TOPRIGHT", 0, 0)
            scrollBar:SetPoint("BOTTOMRIGHT", detailFrame, "BOTTOMRIGHT", 0, 0)
            scrollBar:SetWidth(16)
            scrollBar:SetOrientation("VERTICAL")
            scrollBar:SetMinMaxValues(0, math.max(0, playerCount * 18 - playerDisplayHeight))
            scrollBar:SetValueStep(18)
            scrollBar:SetValue(0)
            
            local scrollBg = scrollBar:CreateTexture(nil, "BACKGROUND")
            scrollBg:SetAllPoints(scrollBar)
            scrollBg:SetTexture(0.2, 0.2, 0.2, 0.8)
            
            local scrollThumb = scrollBar:CreateTexture(nil, "OVERLAY")
            scrollThumb:SetWidth(16)
            scrollThumb:SetHeight(20)
            scrollThumb:SetTexture(0.6, 0.6, 0.6, 1)
            scrollBar:SetThumbTexture(scrollThumb)
            
            scrollBar:SetScript("OnValueChanged", function()
                local value = this:GetValue()
                scrollFrame:SetVerticalScroll(value)
            end)
            
            scrollFrame:EnableMouseWheel(true)
            scrollFrame:SetScript("OnMouseWheel", function()
                local delta = arg1
                local newValue = scrollBar:GetValue() - (delta * 18)
                local minVal, maxVal = scrollBar:GetMinMaxValues()
                newValue = math.max(minVal, math.min(maxVal, newValue))
                scrollBar:SetValue(newValue)
            end)
            
            detailFrame.scrollFrame = scrollFrame
            detailFrame.scrollChild = scrollChild
            detailFrame.scrollBar = scrollBar
        end
        
        local playerYOffset = 0
        local playerParent = scrollChild or detailFrame
        
        for i, playerInfo in ipairs(raidGroup.players) do
            local playerText = playerParent:CreateFontString(nil, "OVERLAY", "GameFontNormalSmall")
            playerText:SetPoint("TOPLEFT", playerParent, "TOPLEFT", 35, -playerYOffset)  -- 从35改为30，向左移动5像素
            
            local playerClassToken = playerInfo.class
            local hexColor = RaidCalendar_GetClassColorHex(playerClassToken)
            
            local instanceId = playerInfo.id or "未知"
            local idDisplayColorHex = "FFFFFF"

            if idCounts[instanceId] and idCounts[instanceId] > 1 then
                if instanceIdToColorMap[instanceId] then
                    idDisplayColorHex = instanceIdToColorMap[instanceId]
                else
                    if table.getn(RaidCalendar.sharedIdColors) > 0 then
                        idDisplayColorHex = RaidCalendar.sharedIdColors[nextSharedColorIndex]
                        instanceIdToColorMap[instanceId] = idDisplayColorHex
                        nextSharedColorIndex = nextSharedColorIndex + 1
                        if nextSharedColorIndex > table.getn(RaidCalendar.sharedIdColors) then
                            nextSharedColorIndex = 1
                        end
                    end
                end
            end
            
            local displayText = "|cFF" .. hexColor .. playerInfo.name .. "|r - ID: |cFF" .. idDisplayColorHex .. instanceId .. "|r"
            
            playerText:SetText(displayText)
            playerYOffset = playerYOffset + 18
        end
        
        -- 修复展开/收缩显示逻辑
        if raidGroup.expanded then
            detailFrame:Show()
        else
            detailFrame:Hide()
        end
        
        expandButton:SetScript("OnClick", function()
            local currentParentFrame = this.parentFrame
            
            if this.expanded then
                -- 收缩
                this.expanded = false
                currentParentFrame.raidGroupStates[this.raidName] = false
                if this.expandIcon then
                    this.expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
                end
                if this.detailFrame then
                    this.detailFrame:Hide()
                end
            else
                -- 先收缩其他所有展开的项目
                for stateName, _ in pairs(currentParentFrame.raidGroupStates) do
                    if stateName ~= this.raidName then
                        currentParentFrame.raidGroupStates[stateName] = false
                    end
                end
                
                -- 展开当前项目
                this.expanded = true
                currentParentFrame.raidGroupStates[this.raidName] = true
                if this.expandIcon then
                    this.expandIcon:SetTexture("Interface\\Buttons\\UI-MinusButton-Up")
                end
                if this.detailFrame then
                    this.detailFrame:Show()
                end
            end
            
            -- 重新布局：需要重新计算所有元素位置，但保持展开状态
            RaidCalendar_RelayoutRaidElements(currentParentFrame)
        end)
        
        table.insert(parentFrame.raidElements, expandButton)
        table.insert(parentFrame.raidElements, detailFrame)
        
        if raidGroup.expanded then
            yOffset = yOffset - (25 + playerDisplayHeight)
        else
            yOffset = yOffset - 25
        end
    end
    
    if not hasRaids then
        local noRaidText = parentFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        if parentFrame.raidTitle then
            noRaidText:SetPoint("TOP", parentFrame.raidTitle, "BOTTOM", 0, -80)  -- 向下70像素（原来是-60）
        else
            noRaidText:SetPoint("TOP", parentFrame, "TOP", 0, yOffset)  -- 备用锚点
        end
        noRaidText:SetText("当前无副本记录")
        noRaidText:SetTextColor(0.5, 0.5, 0.5)
        noRaidText.elementType = "raidRecord"
        table.insert(parentFrame.raidElements, noRaidText)
    end
    
    -- 删除：不再调用主文件的窗口大小调整函数
    -- if type(RaidCalendar_AdjustRightFrameSize) == "function" then
    --     RaidCalendar_AdjustRightFrameSize()
    -- end
end

-- 移入：共享ID颜色配置（从主文件移入）
RaidCalendar.sharedIdColors = {
    "FFFF00",
    "00FFFF", 
    "FF00FF",
    "FFA500",
    "32CD32",
    "FF69B4",
    "ADFF2F",
    "FF4500",
    "1E90FF",
}

-- 移入：副本图标映射表（从主文件移入）
RaidCalendar.raidIconMap = {
    ["熔火之心"] = "Interface\\AddOns\\RaidCalendar\\textures\\MC_Icon.tga",
    ["黑翼之巢"] = "Interface\\AddOns\\RaidCalendar\\textures\\BWL_Icon.tga",
    ["安其拉神殿"] = "Interface\\AddOns\\RaidCalendar\\textures\\TAQ_Icon.tga",
    ["纳克萨玛斯"] = "Interface\\AddOns\\RaidCalendar\\textures\\NAXX_Icon.tga",
    ["翡翠圣地"] = "Interface\\AddOns\\RaidCalendar\\textures\\FC_Icon.tga",
    ["卡拉赞之塔"] = "Interface\\AddOns\\RaidCalendar\\textures\\K40_Icon.tga",
    ["奥妮克希亚的巢穴"] = "Interface\\AddOns\\RaidCalendar\\textures\\HL_Icon.tga",
    ["卡拉赞"] = "Interface\\AddOns\\RaidCalendar\\textures\\KLZ_Icon.tga",
    ["祖尔格拉布"] = "Interface\\AddOns\\RaidCalendar\\textures\\ZG_Icon.tga",
    ["安其拉废墟"] = "Interface\\AddOns\\RaidCalendar\\textures\\AQ20_Icon.tga",
}

-- 移入：副本图标获取函数（从主文件移入）
function RaidCalendar_GetRaidIcon(raidName)
    if not raidName then
        return nil
    end
    
    if RaidCalendar.raidIconMap[raidName] then
        return RaidCalendar.raidIconMap[raidName]
    end
    
    for mapName, iconPath in pairs(RaidCalendar.raidIconMap) do
        if string.find(raidName, mapName) or string.find(mapName, raidName) then
            return iconPath
        end
    end
    
    return nil
end

-- 新增：强制清理错误的数据结构
function RaidCalendar_CleanupDataStructure()
    if not RaidCalendarDB then
        RaidCalendarDB = {
            characters = {},
            guildRaids = {}
        }
        return
    end
    
    -- 清理 characters 下错误的 guildRaids 结构
    if RaidCalendarDB.characters and RaidCalendarDB.characters.guildRaids then
        RaidCalendarDB.characters.guildRaids = nil
    end
    
    -- 清理其他可能的错误结构
    if RaidCalendarDB.characters then
        for charName, charData in pairs(RaidCalendarDB.characters) do
            if type(charData) == "table" and charData.guildRaids then
                charData.guildRaids = nil
            end
        end
    end
    
    -- 清理重复的数据：RaidCalendarDB.characters 应该只存储个人角色数据，不应该有公会数据
    if RaidCalendarDB.characters then
        for charName, charData in pairs(RaidCalendarDB.characters) do
            if type(charData) == "table" then
                -- 只保留必要的字段
                local cleanData = {
                    name = charData.name or charName,
                    raids = charData.raids or {},
                    lastUpdate = charData.lastUpdate or time()
                }
                RaidCalendarDB.characters[charName] = cleanData
            end
        end
    end
    
    -- 确保基本结构存在
    if not RaidCalendarDB.characters then
        RaidCalendarDB.characters = {}
    end
    
    if not RaidCalendarDB.guildRaids then
        RaidCalendarDB.guildRaids = {}
    end
    
    -- 清理过时的数据结构
    if RaidCalendarDB.profile then
        RaidCalendarDB.profile = nil
    end
    
    if RaidCalendarDB.global then
        RaidCalendarDB.global = nil
    end
end

-- 新增：强制清理所有没有时间戳的旧数据
function RaidCalendar_CleanupLegacyData()
    if not RaidCalendarDB then
        return 0
    end
    
    local cleanupCount = 0
    
    -- 清理角色数据中没有时间戳的旧数据
    if RaidCalendarDB.characters then
        for characterName, characterData in pairs(RaidCalendarDB.characters) do
            if characterData and characterData.raids then
                local cleanRaids = {}
                
                for _, raidInfo in ipairs(characterData.raids) do
                    -- 只保留有8位明文时间戳的数据
                    if raidInfo.readableTime and type(raidInfo.readableTime) == "number" and 
                       raidInfo.readableTime >= 1000000 and raidInfo.readableTime <= 99999999 then
                        local cleanRaidInfo = {
                            name = raidInfo.name,
                            id = raidInfo.id,
                            -- 删除：不再保存reset字段
                            -- reset = raidInfo.reset,
                            readableTime = raidInfo.readableTime
                        }
                        table.insert(cleanRaids, cleanRaidInfo)
                    else
                        cleanupCount = cleanupCount + 1
                    end
                end
                
                characterData.raids = cleanRaids
            end
        end
    end
    
    -- 清理工会数据中没有时间戳的旧数据
    if RaidCalendarDB.guildRaids then
        for playerName, playerRaids in pairs(RaidCalendarDB.guildRaids) do
            if type(playerRaids) == "table" then
                for raidName, raidEntries in pairs(playerRaids) do
                    if type(raidEntries) == "table" then
                        local cleanEntries = {}
                        
                        for _, raidInfo in ipairs(raidEntries) do
                            -- 只保留有8位明文时间戳的数据
                            if raidInfo.readableTime and type(raidInfo.readableTime) == "number" and 
                               raidInfo.readableTime >= 1000000 and raidInfo.readableTime <= 99999999 then
                                table.insert(cleanEntries, raidInfo)
                            else
                                cleanupCount = cleanupCount + 1
                            end
                        end
                        
                        if next(cleanEntries) ~= nil then
                            playerRaids[raidName] = cleanEntries
                        else
                            playerRaids[raidName] = nil
                        end
                    end
                end
                
                -- 如果该玩家没有任何有效副本数据，清理整个条目
                if next(playerRaids) == nil then
                    RaidCalendarDB.guildRaids[playerName] = nil
                end
            end
        end
    end
    
    return cleanupCount
end

function RaidCalendar_SaveCurrentCharacterRaids()
    local playerName = GetUnitName("player")
    
    if not playerName then
        return
    end
    
    -- 在保存前先清理数据结构（但不清理过期数据，交给定时系统处理）
    RaidCalendar_CleanupDataStructure()
    
    -- 记录保存前的数据哈希
    local oldHash = RaidCalendar_GetDataHash()
    
    -- 确保角色数据结构正确 - 只存储在 characters 中
    if not RaidCalendarDB.characters[playerName] then
        RaidCalendarDB.characters[playerName] = {
            name = playerName,
            raids = {},
            lastUpdate = RaidCalendar_GetReadableTimestamp()
        }
    end
    
    local raids = {}
    
    -- 检查API可用性
    if not GetNumSavedInstances then
        return
    end
    
    local numSavedInstances = GetNumSavedInstances()
    
    if numSavedInstances and numSavedInstances > 0 then
        for i = 1, numSavedInstances do
            if GetSavedInstanceInfo then
                local instanceName, instanceID, instanceReset = GetSavedInstanceInfo(i)
                
                if instanceName and instanceID then
                    table.insert(raids, {
                        name = instanceName,
                        id = instanceID,
                        -- 删除：不保存reset字段，因为当前角色每次登录都会重新扫描
                        -- reset = instanceReset or 0,
                        readableTime = RaidCalendar_GetReadableTimestamp() -- 只使用明文时间戳
                    })
                end
            else
                break
            end
        end
    end
    
    -- 更新角色数据 - 只存储个人数据
    RaidCalendarDB.characters[playerName].raids = raids
    RaidCalendarDB.characters[playerName].lastUpdate = RaidCalendar_GetReadableTimestamp()
    
    -- 检查数据是否有变化，如有变化则主动同步
    local newHash = RaidCalendar_GetDataHash()
    if oldHash ~= newHash then
        RaidCalendar.lastDataHash = newHash
        
        -- 延迟一小段时间再同步，确保数据保存完成
        local syncFrame = CreateFrame("Frame")
        local syncElapsed = 0
        syncFrame:SetScript("OnUpdate", function()
            syncElapsed = syncElapsed + arg1
            if syncElapsed >= 1 then -- 延迟1秒
                syncFrame:SetScript("OnUpdate", nil)
                RaidCalendar_CheckDataChangeAndSync()
            end
        end)
    end
end

function RaidCalendar_SyncToGuild()
    local currentCharacter = RaidCalendar_GetCurrentCharacterName()
    
    if not RaidCalendar_IsCharacterInWhitelist(currentCharacter) then
        return
    end
    
    if not IsInGuild() then
        return
    end
    
    -- 保存当前角色数据
    RaidCalendar_SaveCurrentCharacterRaids()
    
    -- 使用通用同步函数
    RaidCalendar_SendSyncMessage("GUILD")
end

function RaidCalendar_ChatMsgAddonHandler(prefix, message, channel, sender)
    if prefix ~= RaidCalendar.MSG_PREFIX then
        return
    end
    
    -- 只处理工会频道消息
    if channel ~= "GUILD" then
        return
    end
    
    -- 确保交换系统已初始化
    RaidCalendar_InitExchangeSystem()
    
    -- 严格的白名单检查：发送者必须在白名单中
    if not RaidCalendar_IsCharacterInWhitelist(sender) then
        return
    end
    
    -- 额外检查：接收者（当前角色）也必须在白名单中才能处理消息
    local currentCharacter = RaidCalendar_GetCurrentCharacterName()
    if not RaidCalendar_IsCharacterInWhitelist(currentCharacter) then
        return
    end
    
    if not RaidCalendarDB then
        RaidCalendarDB = {
            characters = {},
            guildRaids = {}
        }
    end
    
    if not RaidCalendarDB.guildRaids then
        RaidCalendarDB.guildRaids = {}
    end

    -- 检查是否是回复消息
    local isReplyMessage = false
    local actualMessage = message
    if string.sub(message, 1, 6) == "REPLY:" then
        isReplyMessage = true
        actualMessage = string.sub(message, 7)
    end

    -- 处理接收到的数据
    if actualMessage == "NO_RAID_LOCKS" then
        RaidCalendarDB.guildRaids[sender] = {}
    else
        local receivedRaids = {}
        for raidEntry in string.gmatch(actualMessage, "([^;]+)") do
            local parts = {}
            for part in string.gmatch(raidEntry, "([^,]+)") do
                table.insert(parts, part)
            end
            if table.getn(parts) >= 4 then
                local raidName = parts[1]
                local raidId = tonumber(parts[2])
                local className = parts[3]
                local readableTime = tonumber(parts[4])
                
                if raidName and raidId and readableTime then
                    if not receivedRaids[raidName] then
                        receivedRaids[raidName] = {}
                    end
                    table.insert(receivedRaids[raidName], {
                        id = raidId, 
                        class = className,
                        readableTime = readableTime -- 只存储明文时间戳
                    })
                end
            end
            -- 删除：不再兼容旧格式，只接受新的明文时间戳格式
        end
        
        -- 存储接收到的数据（直接覆盖旧数据）
        RaidCalendarDB.guildRaids[sender] = receivedRaids
    end
    
    -- 数据交换机制：大幅减少回复频率
    if sender ~= GetUnitName("player") and not isReplyMessage then
        local currentTime = time()
        local lastExchangeTime = RaidCalendar.lastExchangeTime[sender] or 0
        
        -- 检查是否在冷却期内（增加到5分钟）
        if currentTime - lastExchangeTime < RaidCalendar.exchangeCooldown then
            -- 刷新界面但不发送回复
            if RaidCalendar.rightPanel then
                RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
            end
            return
        end
        
        -- 全局同步限制检查
        local globalLastSync = RaidCalendar.globalSyncLimiter.lastGlobalSyncTime or 0
        if currentTime - globalLastSync < RaidCalendar.globalSyncLimiter.globalSyncCooldown then
            -- 全局冷却期内，不发送回复
            if RaidCalendar.rightPanel then
                RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
            end
            return
        end
        
        -- 记录本次交换时间
        RaidCalendar.lastExchangeTime[sender] = currentTime
        RaidCalendar.globalSyncLimiter.lastGlobalSyncTime = currentTime
        
        -- 添加大幅随机延迟，避免同时回复造成网络拥堵
        local exchangeDelay = math.random(30, 120) -- 30-120秒随机延迟
        
        local exchangeFrame = CreateFrame("Frame")
        local exchangeElapsed = 0
        exchangeFrame:SetScript("OnUpdate", function()
            exchangeElapsed = exchangeElapsed + arg1
            if exchangeElapsed >= exchangeDelay then
                exchangeFrame:SetScript("OnUpdate", nil)
                
                -- 只回复到工会频道
                if IsInGuild() then
                    -- 发送自己的数据作为回复
                    RaidCalendar_SaveCurrentCharacterRaids()
                    
                    local playerName = GetUnitName("player")
                    if playerName and RaidCalendarDB and RaidCalendarDB.characters and RaidCalendarDB.characters[playerName] then
                        local playerRaids = RaidCalendarDB.characters[playerName].raids or {}
                        
                        if next(playerRaids) ~= nil then
                            local _, playerClassToken = UnitClass("player")
                            if not playerClassToken then playerClassToken = "UNKNOWN" end
                            
                            local messageParts = {}
                            for _, raidInfo in pairs(playerRaids) do
                                local name = raidInfo.name or "UnknownRaid"
                                local id = raidInfo.id or 0
                                local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
                                table.insert(messageParts, name .. "," .. id .. "," .. playerClassToken .. "," .. readableTime)
                            end
                            local replyMessage = "REPLY:" .. table.concat(messageParts, ";")
                            
                            if SendAddonMessage then
                                SendAddonMessage(RaidCalendar.MSG_PREFIX, replyMessage, "GUILD")
                            end
                        else
                            -- 即使没有副本数据也要回复，表示在线
                            if SendAddonMessage then
                                SendAddonMessage(RaidCalendar.MSG_PREFIX, "REPLY:NO_RAID_LOCKS", "GUILD")
                            end
                        end
                    end
                end
            end
        end)
    end
    
    -- 刷新界面
    if RaidCalendar.rightPanel then
        RaidCalendar_UpdateRaidInfo(RaidCalendar.rightPanel)
    end
end

-- 新增：通用同步消息发送函数
function RaidCalendar_SendSyncMessage(channel)
    local playerName = GetUnitName("player")
    if not playerName then
        return
    end
    
    if not RaidCalendarDB or not RaidCalendarDB.characters or not RaidCalendarDB.characters[playerName] or not RaidCalendarDB.characters[playerName].raids then
        if SendAddonMessage then
            SendAddonMessage(RaidCalendar.MSG_PREFIX, "NO_RAID_LOCKS", channel)
        end
        return
    end
    
    local playerRaids = RaidCalendarDB.characters[playerName].raids
    
    if next(playerRaids) == nil then
        if SendAddonMessage then
            SendAddonMessage(RaidCalendar.MSG_PREFIX, "NO_RAID_LOCKS", channel)
        end
        return
    end
    
    local _, playerClassToken = UnitClass("player")
    if not playerClassToken then playerClassToken = "UNKNOWN" end

    local messageParts = {}
    for _, raidInfo in pairs(playerRaids) do
        local name = raidInfo.name or "UnknownRaid"
        local id = raidInfo.id or 0
        local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
        table.insert(messageParts, name .. "," .. id .. "," .. playerClassToken .. "," .. readableTime)
    end
    local message = table.concat(messageParts, ";")
    
    if message ~= "" and SendAddonMessage then
        SendAddonMessage(RaidCalendar.MSG_PREFIX, message, channel)
    elseif SendAddonMessage then
        SendAddonMessage(RaidCalendar.MSG_PREFIX, "NO_RAID_LOCKS", channel)
    end
end

-- 修改：大幅增加冷却时间，移除定期同步
function RaidCalendar_InitExchangeSystem()
    -- 确保交换冷却系统已初始化
    if not RaidCalendar.lastExchangeTime then
        RaidCalendar.lastExchangeTime = {}
    end
    
    if not RaidCalendar.exchangeCooldown then
        RaidCalendar.exchangeCooldown = 300  -- 增加到5分钟冷却时间
    end
end

-- 新增：全局同步限制和队列机制
RaidCalendar.globalSyncLimiter = {
    lastGlobalSyncTime = 0,
    globalSyncCooldown = 120, -- 全局2分钟内只允许一次同步
    loginSyncQueue = {},
    isProcessingQueue = false
}

-- 新增：登录同步队列处理
function RaidCalendar_ProcessLoginSyncQueue()
    DEFAULT_CHAT_FRAME:AddMessage("=== 开始处理登录同步队列 ===")
    
    if RaidCalendar.globalSyncLimiter.isProcessingQueue then
        DEFAULT_CHAT_FRAME:AddMessage("队列处理: 队列正在处理中，退出")
        return
    end
    
    local queue = RaidCalendar.globalSyncLimiter.loginSyncQueue
    if not queue or table.getn(queue) == 0 then
        DEFAULT_CHAT_FRAME:AddMessage("队列处理: 队列为空，退出")
        return
    end
    
    -- 限制队列大小，防止内存泄露
    local maxQueueSize = 5
    if table.getn(queue) > maxQueueSize then
        DEFAULT_CHAT_FRAME:AddMessage("队列处理: 队列过大，只保留最新的 " .. maxQueueSize .. " 个项目")
        local newQueue = {}
        local startIndex = table.getn(queue) - maxQueueSize + 1
        for i = startIndex, table.getn(queue) do
            table.insert(newQueue, queue[i])
        end
        RaidCalendar.globalSyncLimiter.loginSyncQueue = newQueue
        queue = newQueue
    end
    
    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 开始处理 " .. table.getn(queue) .. " 个项目")
    RaidCalendar.globalSyncLimiter.isProcessingQueue = true
    
    -- 每次只处理一个，间隔30秒
    local processFrame = CreateFrame("Frame")
    local processInterval = 30 -- 30秒间隔
    local currentIndex = 1
    
    local function processNext()
        if currentIndex <= table.getn(queue) then
            local syncData = queue[currentIndex]
            DEFAULT_CHAT_FRAME:AddMessage("队列处理: 处理第 " .. currentIndex .. " 个项目")
            
            if syncData and syncData.playerName and syncData.message then
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 发送消息 - 角色: " .. syncData.playerName)
                
                -- 发送消息
                if SendAddonMessage and RaidCalendar.MSG_PREFIX then
                    SendAddonMessage(RaidCalendar.MSG_PREFIX, syncData.message, "GUILD")
                    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 消息已发送到工会频道")
                else
                    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 发送消息失败")
                end
            else
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 无效的同步数据，跳过")
            end
            
            currentIndex = currentIndex + 1
            
            -- 继续下一个
            if currentIndex <= table.getn(queue) then
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 等待 " .. processInterval .. " 秒后处理下一个")
                local nextFrame = CreateFrame("Frame")
                local nextElapsed = 0
                nextFrame:SetScript("OnUpdate", function()
                    nextElapsed = nextElapsed + arg1
                    if nextElapsed >= processInterval then
                        nextFrame:SetScript("OnUpdate", nil)
                        processNext()
                    end
                end)
            else
                -- 队列处理完成
                DEFAULT_CHAT_FRAME:AddMessage("队列处理: 所有项目处理完成")
                RaidCalendar.globalSyncLimiter.loginSyncQueue = {}
                RaidCalendar.globalSyncLimiter.isProcessingQueue = false
                DEFAULT_CHAT_FRAME:AddMessage("=== 登录同步队列处理完成 ===")
            end
        end
    end
    
    -- 开始处理第一个
    DEFAULT_CHAT_FRAME:AddMessage("队列处理: 立即开始处理第一个项目")
    processNext()
end

-- 新增：副本记录模块初始化函数
function RaidCalendar_InitRaidRecordsModule()
    -- 强制清理错误的数据结构
    RaidCalendar_CleanupDataStructure()
    
    -- 首先清理所有没有时间戳的旧数据
    local legacyCleanupCount = RaidCalendar_CleanupLegacyData()
    
    -- 然后执行过期数据清理
    local expiredCleanupCount = RaidCalendar_AutoCleanupExpiredRaidData()
end

-- 新增：重新布局副本元素（保持展开状态）
function RaidCalendar_RelayoutRaidElements(parentFrame)
    if not parentFrame or not parentFrame.raidElements then
        return
    end
    
    -- 保存当前展开状态
    local savedStates = {}
    if parentFrame.raidGroupStates then
        for raidName, state in pairs(parentFrame.raidGroupStates) do
            savedStates[raidName] = state
        end
    end
    
    -- 重新布局
    RaidCalendar_UpdateRaidInfo(parentFrame)
    
    -- 恢复展开状态
    if parentFrame.raidGroupStates then
        for raidName, state in pairs(savedStates) do
            parentFrame.raidGroupStates[raidName] = state
        end
    end
    
    -- 根据保存的状态显示/隐藏详情框
    for _, element in ipairs(parentFrame.raidElements) do
        if element and element.raidName and element.detailFrame then
            if savedStates[element.raidName] then
                element.expanded = true
                element.detailFrame:Show()
                if element.expandIcon then
                    element.expandIcon:SetTexture("Interface\\Buttons\\UI-MinusButton-Up")
                end
            else
                element.expanded = false
                element.detailFrame:Hide()
                if element.expandIcon then
                    element.expandIcon:SetTexture("Interface\\Buttons\\UI-PlusButton-Up")
                end
            end
        end
    end
end

-- 新增：简化的数据哈希计算，用于检测变化
function RaidCalendar_GetDataHash()
    local playerName = GetUnitName("player")
    if not playerName or not RaidCalendarDB or not RaidCalendarDB.characters or not RaidCalendarDB.characters[playerName] then
        return "empty"
    end
    
    local raids = RaidCalendarDB.characters[playerName].raids
    if not raids or next(raids) == nil then
        return "empty"
    end
    
    -- 创建一个简单的数据指纹
    local hashParts = {}
    for _, raidInfo in pairs(raids) do
        table.insert(hashParts, (raidInfo.name or "") .. ":" .. (raidInfo.id or ""))
    end
    table.sort(hashParts)
    return table.concat(hashParts, "|")
end

-- 修改：只保留数据变化时的同步
function RaidCalendar_CheckDataChangeAndSync()
    local currentCharacter = RaidCalendar_GetCurrentCharacterName()
    
    if not RaidCalendar_IsCharacterInWhitelist(currentCharacter) then
        return
    end
    
    if not IsInGuild() then
        return
    end
    
    local currentHash = RaidCalendar_GetDataHash()
    
    -- 如果数据有变化，立即同步
    if currentHash ~= RaidCalendar.lastDataHash then
        -- 更新数据指纹
        RaidCalendar.lastDataHash = currentHash
        
        -- 执行同步
        RaidCalendar_SyncToGuild()
        
        -- 更新全局同步时间
        RaidCalendar.globalSyncLimiter.lastGlobalSyncTime = time()
    end
end

-- 删除：禁用组队同步功能
function RaidCalendar_OnPartyRaidJoin()
    -- 完全禁用组队同步以减少消息冲突
    return
end

-- 修改：大幅优化登录同步，使用队列机制，减少消息输出
function RaidCalendar_RaidRecordsOnLogin()
    -- 减少消息输出
    
    -- 增加：检查基本函数是否可用
    local playerName = nil
    if GetUnitName then
        playerName = GetUnitName("player")
    elseif UnitName then
        playerName = UnitName("player")
    end
    
    if not playerName or playerName == "" then
        return
    end
    
    -- 登录时清理一次没有时间戳的旧数据（静默）
    RaidCalendar_CleanupLegacyData()
    
    -- 执行过期数据清理（移除详细输出）
    RaidCalendar_AutoCleanupExpiredRaidData()
    
    -- 检查是否有白名单检查函数
    local currentCharacter = nil
    if type(RaidCalendar_GetCurrentCharacterName) == "function" then
        currentCharacter = RaidCalendar_GetCurrentCharacterName()
    else
        currentCharacter = playerName
    end
    
    -- 检查白名单
    local isInWhitelist = true
    if type(RaidCalendar_IsCharacterInWhitelist) == "function" then
        isInWhitelist = RaidCalendar_IsCharacterInWhitelist(currentCharacter)
    end
    
    if not isInWhitelist then
        return
    end
    
    -- 修改：使用队列机制延迟发送登录同步消息
    if IsInGuild then
        local inGuild = IsInGuild()
        if inGuild then
            -- 大幅延迟登录同步，使用队列机制
            local loginDelay = math.random(180, 600) -- 3-10分钟随机延迟
            
            local loginSyncFrame = CreateFrame("Frame")
            local loginElapsed = 0
            loginSyncFrame:SetScript("OnUpdate", function()
                loginElapsed = loginElapsed + arg1
                if loginElapsed >= loginDelay then
                    loginSyncFrame:SetScript("OnUpdate", nil)
                    
                    -- 保存当前角色副本数据
                    if type(RaidCalendar_SaveCurrentCharacterRaids) == "function" then
                        RaidCalendar_SaveCurrentCharacterRaids()
                    end
                    
                    -- 准备队列消息，但加入队列而不是立即发送
                    if playerName and RaidCalendarDB and RaidCalendarDB.characters and RaidCalendarDB.characters[playerName] then
                        local playerRaids = RaidCalendarDB.characters[playerName].raids or {}
                        local _, playerClassToken = UnitClass("player")
                        if not playerClassToken then playerClassToken = "UNKNOWN" end
                        
                        local message = ""
                        if next(playerRaids) ~= nil then
                            local messageParts = {}
                            for _, raidInfo in pairs(playerRaids) do
                                local name = raidInfo.name or "UnknownRaid"
                                local id = raidInfo.id or 0
                                local readableTime = raidInfo.readableTime or RaidCalendar_GetReadableTimestamp()
                                table.insert(messageParts, name .. "," .. id .. "," .. playerClassToken .. "," .. readableTime)
                            end
                            message = table.concat(messageParts, ";")
                        else
                            message = "NO_RAID_LOCKS"
                        end
                        
                        -- 加入同步队列而不是立即发送
                        if not RaidCalendar.globalSyncLimiter.loginSyncQueue then
                            RaidCalendar.globalSyncLimiter.loginSyncQueue = {}
                        end
                        
                        table.insert(RaidCalendar.globalSyncLimiter.loginSyncQueue, {
                            playerName = playerName,
                            message = message,
                            timestamp = time()
                        })
                        
                        -- 启动队列处理
                        RaidCalendar_ProcessLoginSyncQueue()
                    end
                    
                    -- 初始化数据哈希
                    if type(RaidCalendar_GetDataHash) == "function" then
                        RaidCalendar.lastDataHash = RaidCalendar_GetDataHash()
                    end
                end
            end)
        end
    end
end

-- 修改：副本记录模块的事件监听器，移除组队相关事件
local raidRecordEventFrame = CreateFrame("Frame", "RaidCalendarRaidRecordEventFrame")
raidRecordEventFrame:RegisterEvent("ADDON_LOADED")
raidRecordEventFrame:RegisterEvent("PLAYER_LOGIN")
raidRecordEventFrame:RegisterEvent("CHAT_MSG_ADDON")
-- 移除：不再注册组队相关事件
-- raidRecordEventFrame:RegisterEvent("PARTY_MEMBERS_CHANGED")
-- raidRecordEventFrame:RegisterEvent("RAID_ROSTER_UPDATE")

-- 添加登录处理标记，防止重复执行
local loginProcessed = false

raidRecordEventFrame:SetScript("OnEvent", function()
    if event == "ADDON_LOADED" and arg1 == "RaidCalendar" then
        DEFAULT_CHAT_FRAME:AddMessage("=== 副本记录模块已加载 ===")
        
        -- 安全初始化各个系统
        pcall(function()
            if type(RaidCalendar_InitExchangeSystem) == "function" then
                RaidCalendar_InitExchangeSystem()
            end
        end)
        
        pcall(function()
            if type(RaidCalendar_CleanupDataStructure) == "function" then
                RaidCalendar_CleanupDataStructure()
            end
        end)
        
        pcall(function()
            if type(RaidCalendar_InitRaidRecordsModule) == "function" then
                RaidCalendar_InitRaidRecordsModule()
            end
        end)
        
    elseif event == "PLAYER_LOGIN" and not loginProcessed then
        DEFAULT_CHAT_FRAME:AddMessage("=== 副本记录模块收到PLAYER_LOGIN事件 ===")
        loginProcessed = true  -- 防止重复执行
        
        -- 延迟执行登录清理，避免与主模块冲突
        local loginDelayFrame = CreateFrame("Frame")
        local loginDelay = 0
        loginDelayFrame:SetScript("OnUpdate", function()
            loginDelay = loginDelay + arg1
            if loginDelay >= 1 then -- 延迟1秒执行
                loginDelayFrame:SetScript("OnUpdate", nil)
                
                DEFAULT_CHAT_FRAME:AddMessage("=== 开始执行副本记录登录处理 ===")
                -- 安全执行登录处理
                pcall(function()
                    if type(RaidCalendar_RaidRecordsOnLogin) == "function" then
                        RaidCalendar_RaidRecordsOnLogin()
                    else
                        DEFAULT_CHAT_FRAME:AddMessage("错误: RaidCalendar_RaidRecordsOnLogin 函数不存在")
                    end
                end)
            end
        end)
        
    elseif event == "CHAT_MSG_ADDON" and arg1 == RaidCalendar.MSG_PREFIX then
        -- 安全处理插件消息
        pcall(function()
            if type(RaidCalendar_ChatMsgAddonHandler) == "function" then
                RaidCalendar_ChatMsgAddonHandler(arg1, arg2, arg3, arg4)
            end
        end)
        
    end
end)

-- 3天副本和5天副本的过期判断逻辑如下：

-- 3天副本（如祖尔格拉布、安其拉废墟）：
--   1. 依赖主模块的 RaidCalendar_CheckThreeDayReset 函数和 threeDayResetBaseTimestamp。
--   2. 从副本记录时间起，依次检查未来7天内的每一天。
--   3. 如果某一天是重置日（RaidCalendar_CheckThreeDayReset(checkTime) 返回 true），
--      则当天12:00为重置点。
--   4. 如果当前时间已过该重置点，并且副本记录时间早于该重置点，则判定为过期。
--   5. 否则不算过期。

-- 5天副本（如奥妮克希亚、卡拉赞）：
--   1. 依赖主模块的 RaidCalendar_CheckFiveDayReset 函数和 fiveDayResetBaseTimestamp。
--   2. 从副本记录时间起，依次检查未来8天内的每一天。
--   3. 如果某一天是重置日（RaidCalendar_CheckFiveDayReset(checkTime) 返回 true），
--      则当天12:00为重置点。
--   4. 如果当前时间已过该重置点，并且副本记录时间早于该重置点，则判定为过期。
--   5. 否则不算过期。

-- 伪代码示例（实际代码已在你的文件中）：
-- for checkDay = 1, N do
--     local checkTime = raidTimestamp + (checkDay * 24 * 3600)
--     if CheckResetFunc(checkTime) then
--         local resetTime = 当天12:00
--         if currentTime >= resetTime and raidTimestamp < resetTime then
--             return true -- 过期
--         end
--         break
--     end
-- end
-- return false -- 未过期
