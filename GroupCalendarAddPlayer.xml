<UI xmlns="http://www.blizzard.com/wow/ui/"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.blizzard.com/wow/ui/FrameXML/UI.xsd">
	
	<Frame name="CalendarAddPlayerTemplate" toplevel="true" enableMouse="true" virtual="true">
		<Size>
			<AbsDimension x="465" y="225"/>
		</Size>
		<Backdrop bgFile="Interface\Addons\GroupCalendar\Textures\DialogBox-Background" edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
			<BackgroundInsets>
				<AbsInset left="11" right="12" top="12" bottom="11"/>
			</BackgroundInsets>
			<TileSize>
				<AbsValue val="32"/>
			</TileSize>
			<EdgeSize>
				<AbsValue val="32"/>
			</EdgeSize>
		</Backdrop>
		<Layers>
			<Layer level="ARTWORK">
				<Texture name="$parentFrameHeader" file="Interface\DialogFrame\UI-DialogBox-Header">
					<Size>
						<AbsDimension x="256" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="12"/>
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
				<FontString inherits="GameFontNormal" text="GroupCalendar_cAddPlayer">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentFrameHeader">
							<Offset>
								<AbsDimension x="0" y="-14"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentWhispers">
				<Size>
					<AbsDimension x="24" y="24"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="13" y="-29"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnEnter>
						CalendarAddPlayerWhisper_OnEnter();
					</OnEnter>
					<OnLeave>
						CalendarAddPlayerWhisper_OnLeave();
					</OnLeave>
					<OnClick>
						CalendarAddPlayerWhisper_Reply();
					</OnClick>
				</Scripts>
				<NormalTexture file="Interface\Addons\GroupCalendar\Textures\AttendanceNoteIcon"/>
				<PushedTexture file="Interface\Addons\GroupCalendar\Textures\AttendanceNoteIcon"/>
				<HighlightTexture file="Interface\Buttons\UI-Panel-MinimizeButton-Highlight" alphaMode="ADD"/>
			</Button>
			<EditBox name="$parentName" letters="12" historyLines="0" autofocus="false" inherits="GroupCalendarInputBoxTemplate">
				<Size>
					<AbsDimension x="130" y="20"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="110" y="-40"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontNormalSmall" text="GroupCalendar_cPlayerName" justifyH="RIGHT">
							<Anchors>
								<Anchor point="RIGHT" relativePoint="LEFT">
									<Offset>
										<AbsDimension x="-12" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnChar>
						Calendar_AutoCompletePlayerName(this);
					</OnChar>
					<OnEditFocusLost>
						CalendarAddPlayer_AutoCompletePlayerInfo();
					</OnEditFocusLost>
					<OnEscapePressed>
						this:ClearFocus();
					</OnEscapePressed>
					<OnEnterPressed>
						CalendarAddPlayer_Done();
					</OnEnterPressed>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
			<Frame name="$parentGuildRank" inherits="CalendarPlayerRankMenuTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentName" relativePoint="TOPLEFT">
						<Offset>
							<AbsDimension x="0" y="-30"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="$parentStatus" inherits="CalendarPlayerStatusMenuTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentGuildRank">
						<Offset>
							<AbsDimension x="0" y="-70"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<EditBox name="$parentLevel" letters="2" historyLines="0" autofocus="false" inherits="GroupCalendarInputBoxTemplate">
				<Size>
					<AbsDimension x="40" y="20"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentName" relativePoint="TOPLEFT">
						<Offset>
							<AbsDimension x="200" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontNormalSmall" text="GroupCalendar_cPlayerLevel" justifyH="RIGHT">
							<Anchors>
								<Anchor point="RIGHT" relativePoint="LEFT">
									<Offset>
										<AbsDimension x="-12" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnEscapePressed>
						this:ClearFocus();
					</OnEscapePressed>
					<OnEnterPressed>
						CalendarAddPlayer_Done();
					</OnEnterPressed>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
			<Frame name="$parentClass" inherits="CalendarPlayerClassMenuTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentLevel" relativePoint="TOPLEFT">
						<Offset>
							<AbsDimension x="0" y="-30"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="$parentRace" inherits="CalendarPlayerRaceMenuTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentClass" relativePoint="TOPLEFT">
						<Offset>
							<AbsDimension x="0" y="-30"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<EditBox name="$parentComment" letters="50" historyLines="0" autofocus="false" inherits="GroupCalendarInputBoxTemplate">
				<Size>
					<AbsDimension x="130" y="20"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentRace">
						<Offset>
							<AbsDimension x="0" y="-40"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontNormalSmall" text="GroupCalendar_cCommentLabel" justifyH="RIGHT">
							<Anchors>
								<Anchor point="RIGHT" relativePoint="LEFT">
									<Offset>
										<AbsDimension x="-12" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Scripts>
					<OnEscapePressed>
						this:ClearFocus();
					</OnEscapePressed>
					<OnEnterPressed>
						CalendarAddPlayer_Done();
					</OnEnterPressed>
				</Scripts>
				<FontString inherits="ChatFontNormal"/>
			</EditBox>
			<Frame name="$parentWhisper">
				<Size>
					<AbsDimension x="330" y="60"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parentStatus">
						<Offset>
							<AbsDimension x="0" y="-20"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="ARTWORK">
						<FontString name="$parentRecent" inherits="GameFontNormalSmall" text="Testing" justifyH="LEFT">
							<Size>
								<AbsDimension x="300" y="20"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="0" y="-22"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
						<FontString inherits="GameFontNormalSmall" text="GroupCalendar_cLastWhisper" justifyH="RIGHT">
							<Anchors>
								<Anchor point="RIGHT" relativeTo="$parentRecent" relativePoint="LEFT">
									<Offset>
										<AbsDimension x="-12" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<EditBox name="$parentReply" letters="200" historyLines="0" autofocus="false" inherits="GroupCalendarInputBoxTemplate">
						<Size>
							<AbsDimension x="330" y="20"/>
						</Size>
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parentRecent">
								<Offset>
									<AbsDimension x="0" y="-20"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Layers>
							<Layer level="BACKGROUND">
								<FontString inherits="GameFontNormalSmall" text="GroupCalendar_cReplyWhisper" justifyH="RIGHT">
									<Anchors>
										<Anchor point="RIGHT" relativePoint="LEFT">
											<Offset>
												<AbsDimension x="-12" y="0"/>
											</Offset>
										</Anchor>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Scripts>
							<OnEscapePressed>
								this:ClearFocus();
							</OnEscapePressed>
							<OnEnterPressed>
								CalendarAddPlayer_Done();
							</OnEnterPressed>
						</Scripts>
						<FontString inherits="ChatFontNormal"/>
					</EditBox>
				</Frames>
			</Frame>
			<Button name="$parentSaveButton" inherits="UIPanelButtonTemplate" text="GroupCalendar_cSave">
				<Size>
					<AbsDimension x="80" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="20" y="23"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound("igMainMenuOptionCheckBoxOn");
						CalendarAddPlayer_SaveNext();
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentDeleteButton" inherits="UIPanelButtonTemplate" text="CalendarEventEditor_cDelete">
				<Size>
					<AbsDimension x="80" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativeTo="$parentSaveButton" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="25" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound("igMainMenuOptionCheckBoxOn");
						CalendarAddPlayer_Delete();
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentCancelButton" inherits="UIPanelButtonTemplate" text="CANCEL">
				<Size>
					<AbsDimension x="80" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="-20" y="23"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound("igMainMenuOptionCheckBoxOn");
						CalendarAddPlayer_Cancel();
					</OnClick>
				</Scripts>
			</Button>
			<Button name="$parentDoneButton" inherits="UIPanelButtonTemplate" text="CalendarEventViewer_cDone">
				<Size>
					<AbsDimension x="80" y="21"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="$parentCancelButton" relativePoint="LEFT">
						<Offset>
							<AbsDimension x="-12" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnClick>
						PlaySound("igMainMenuOptionCheckBoxOn");
						CalendarAddPlayer_Done();
					</OnClick>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				this.NormalHeight = this:GetHeight();
			</OnLoad>
			<OnShow>
				GroupCalendar_BeginModalDialog(this);
			</OnShow>
			<OnHide>
				GroupCalendar_EndModalDialog(this);
			</OnHide>
		</Scripts>
	</Frame>
</UI>
