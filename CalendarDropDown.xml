<UI xmlns="http://www.blizzard.com/wow/ui/"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.blizzard.com/wow/ui/FrameXML/UI.xsd">
	
	<Frame name="CalendarDropDownTemplate" inherits="UIDropDownMenuTemplate" hidden="false" virtual="true">
		<Layers>
			<Layer level="OVERLAY">
				<FontString name="$parentTitle" inherits="GameFontNormalSmall">
					<Anchors>
						<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="14" y="13"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
	</Frame>

	<Frame name="CalendarInputFrameTemplate" hidden="false" virtual="true">
		<Scripts>
			<OnShow>
				Calendar_InputFrameSizeChanged(this);
			</OnShow>
			<OnSizeChanged>
				Calendar_InputFrameSizeChanged(this);
			</OnSizeChanged>
		</Scripts>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentTopLeft" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="0" y="4"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0" right="0.046875" top="0" bottom="0.1875"/>
				</Texture>
				<Texture name="$parentLeft" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentTopLeft" relativePoint="BOTTOM"/>
					</Anchors>
					<TexCoords left="0" right="0.046875" top="0.1875" bottom="0.4375"/>
				</Texture>
				<Texture name="$parentBottomLeft" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOP" relativeTo="$parentLeft" relativePoint="BOTTOM"/>
					</Anchors>
					<TexCoords left="0" right="0.046875" top="0.4375" bottom="0.625"/>
				</Texture>
				<Texture name="$parentTop" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentTopLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.046875" right="0.953125" top="0" bottom="0.1875"/>
				</Texture>
				<Texture name="$parentCenter" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.046875" right="0.953125" top="0.1875" bottom="0.4375"/>
				</Texture>
				<Texture name="$parentBottom" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentBottomLeft" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0.046875" right="0.953125" top="0.4375" bottom="0.625"/>
				</Texture>
				<Texture name="$parentTopRight" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTop" relativePoint="TOPRIGHT"/>
					</Anchors>
					<TexCoords left="0.953125" right="1.0" top="0" bottom="0.1875"/>
				</Texture>
				<Texture name="$parentRight" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentTopRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.953125" right="1.0" top="0.1875" bottom="0.4375"/>
				</Texture>
				<Texture name="$parentBottomRight" file="Interface\Common\Common-Input-Border">
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="$parentRight" relativePoint="BOTTOMLEFT"/>
					</Anchors>
					<TexCoords left="0.953125" right="1.0" top="0.4375" bottom="0.625"/>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
</UI>
